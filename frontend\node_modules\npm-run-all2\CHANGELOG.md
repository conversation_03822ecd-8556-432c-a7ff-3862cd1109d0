# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

## [v8.0.4](https://github.com/bcomnes/npm-run-all2/compare/v8.0.4-beta.0...v8.0.4)

### Commits

- Merge pull request #182 from bcomnes/beta [`afcc18a`](https://github.com/bcomnes/npm-run-all2/commit/afcc18a7c68805ae1678871b31cd5103691a4bce)

## [v8.0.4-beta.0](https://github.com/bcomnes/npm-run-all2/compare/v8.0.3...v8.0.4-beta.0) - 2025-05-20

### Merged

- add tests for double stars [`#179`](https://github.com/bcomnes/npm-run-all2/pull/179)

### Commits

- Merge pull request #180 from bcomnes/simplify-lifecycles [`fb6721a`](https://github.com/bcomnes/npm-run-all2/commit/fb6721a1d537f0db9bb87645f158a573343c717a)
- fix new test [`3c0d2b2`](https://github.com/bcomnes/npm-run-all2/commit/3c0d2b29e32c07f2a943d5c9451843a57664fcc8)
- Improve npm lifecycles [`530288e`](https://github.com/bcomnes/npm-run-all2/commit/530288e7d2985dbb7dcc8f6f6347c0c761d3b4af)

## [v8.0.3](https://github.com/bcomnes/npm-run-all2/compare/v8.0.2...v8.0.3) - 2025-05-20

### Commits

- Merge pull request #175 from bcomnes/revert-pico [`75bb00e`](https://github.com/bcomnes/npm-run-all2/commit/75bb00e0a13d96cb4f873fd2967ea3ebeaffd815)
- Revert "replace minimatch with picomatch" [`5d93a54`](https://github.com/bcomnes/npm-run-all2/commit/5d93a5485a5749b2b171c24f4e6e9fe0d64299f6)

## [v8.0.2](https://github.com/bcomnes/npm-run-all2/compare/v8.0.1...v8.0.2) - 2025-05-16

### Commits

- Merge pull request #172 from beeequeue/picomatch [`4f42923`](https://github.com/bcomnes/npm-run-all2/commit/4f42923e1fdea84fc97796e774a5550e8f78f092)
- replace minimatch with picomatch [`74201f5`](https://github.com/bcomnes/npm-run-all2/commit/74201f5f7238d9a2c9d5e7b889db8748687fd4f9)
- add type field [`e8f046a`](https://github.com/bcomnes/npm-run-all2/commit/e8f046af4d4c14b61449695c7eb58f6a385ab2b6)

## [v8.0.1](https://github.com/bcomnes/npm-run-all2/compare/v8.0.0...v8.0.1) - 2025-05-02

### Commits

- Merge pull request #159 from bcomnes/dependabot/npm_and_yarn/minimatch-10.0.1 [`54af3ab`](https://github.com/bcomnes/npm-run-all2/commit/54af3abd3d9fe5c32d21c8bac8a5ef12b7b448aa)
- Upgrade: Bump minimatch from 9.0.5 to 10.0.1 [`f50ddf4`](https://github.com/bcomnes/npm-run-all2/commit/f50ddf48a0622d5473a7f9480069ebec8c5acd45)
- Merge pull request #170 from bcomnes/fix-engine-range [`e43e670`](https://github.com/bcomnes/npm-run-all2/commit/e43e6705fedfa87b8d80200f9d55ceb943ad77c7)

## [v8.0.0](https://github.com/bcomnes/npm-run-all2/compare/v7.0.2...v8.0.0) - 2025-05-02

### Commits

- **Breaking change:** Raise engine floor to &gt;= Node 20 [`b1b12f8`](https://github.com/bcomnes/npm-run-all2/commit/b1b12f858291a7b3d73b20b28ca134407d927166)
- Merge pull request #169 from bcomnes/node-20-or-greater [`bb27458`](https://github.com/bcomnes/npm-run-all2/commit/bb274582dadbaaeeb2c3a2c54b4171660fb92399)

## [v7.0.2](https://github.com/bcomnes/npm-run-all2/compare/v7.0.1...v7.0.2) - 2024-12-16

### Merged

- Upgrade: Bump neostandard from 0.11.9 to 0.12.0 [`#164`](https://github.com/bcomnes/npm-run-all2/pull/164)
- Upgrade: Bump codecov/codecov-action from 4 to 5 [`#161`](https://github.com/bcomnes/npm-run-all2/pull/161)

### Commits

- Raise cross-spawn version floor to ^7.0.6 [`45a9e19`](https://github.com/bcomnes/npm-run-all2/commit/45a9e191b75e0b76433409808b6219d1f4dffe83)
- Merge pull request #163 from bcomnes/dependabot/npm_and_yarn/mocha-11.0.1 [`285967a`](https://github.com/bcomnes/npm-run-all2/commit/285967a3fc5076d8c22ecb14823eb6fc274dd835)
- Upgrade: Bump mocha from 10.8.2 to 11.0.1 [`5d1aea5`](https://github.com/bcomnes/npm-run-all2/commit/5d1aea58f17abd201515f737c06bbe8044d05d21)

## [v7.0.1](https://github.com/bcomnes/npm-run-all2/compare/v7.0.0...v7.0.1) - 2024-10-23

### Commits

- Revert engine range bump back to Node 18 [`b2e849b`](https://github.com/bcomnes/npm-run-all2/commit/b2e849bf8d31135751dd7458724344dd1bca120e)

## [v7.0.0](https://github.com/bcomnes/npm-run-all2/compare/v6.2.6...v7.0.0) - 2024-10-21

### Merged

- Prevent a throw when looking up undefined results [`#158`](https://github.com/bcomnes/npm-run-all2/pull/158)

### Commits

- **Breaking change:** Bump node engines ^18.17.0 || &gt;=20.5.0 [`49b95f0`](https://github.com/bcomnes/npm-run-all2/commit/49b95f0c4905504a94d1a7ce87fbb7e77ab60df5)
- Merge pull request #156 from bcomnes/rm-rf-rimraf [`c661ffc`](https://github.com/bcomnes/npm-run-all2/commit/c661ffc942e09a68b1a553190d3a550dc0f6a39c)
- Remove rimraf [`c77e085`](https://github.com/bcomnes/npm-run-all2/commit/c77e0856c65115b32788c3e9a1e441ba69fbd430)

## [v6.2.6](https://github.com/bcomnes/npm-run-all2/compare/v6.2.4...v6.2.6) - 2024-10-21

### Commits

- Prevent a throw when looking up undefined results [`d928f9a`](https://github.com/bcomnes/npm-run-all2/commit/d928f9ad59c00a20797c90d35b62ef0aecf0c364)

## [v6.2.4](https://github.com/bcomnes/npm-run-all2/compare/v6.2.3...v6.2.4) - 2024-10-18

### Merged

- Switch to JS-based `which` command [`#154`](https://github.com/bcomnes/npm-run-all2/pull/154)

### Fixed

- Switch to JS-based `which` command [`#153`](https://github.com/bcomnes/npm-run-all2/issues/153)

## [v6.2.3](https://github.com/bcomnes/npm-run-all2/compare/v6.2.2...v6.2.3) - 2024-09-13

### Commits

- Merge pull request #151 from bcomnes/fix-pnpm-agian [`c43fa2b`](https://github.com/bcomnes/npm-run-all2/commit/c43fa2b677442c710a29654a333b15c8de4f00ab)
- Avoid passing config fields as flags for pnpm [`dc2d7da`](https://github.com/bcomnes/npm-run-all2/commit/dc2d7da61cf0898d0bc2b25747e588325e06b9a9)

## [v6.2.2](https://github.com/bcomnes/npm-run-all2/compare/v6.2.1...v6.2.2) - 2024-07-04

### Commits

- Revert "Compatibility: npm, yarn and pnpm run scripts" [`fc35f0d`](https://github.com/bcomnes/npm-run-all2/commit/fc35f0dc4f78afc1c631fa94b6ac85ba0fb0e7b1)

## [v6.2.1](https://github.com/bcomnes/npm-run-all2/compare/v6.2.0...v6.2.1) - 2024-07-03

### Merged

- Compatibility: npm, yarn and pnpm run scripts [`#143`](https://github.com/bcomnes/npm-run-all2/pull/143)
- Use neostandard + add more static code analysis  [`#142`](https://github.com/bcomnes/npm-run-all2/pull/142)
- Upgrade: Bump c8 from 9.1.0 to 10.0.0 [`#141`](https://github.com/bcomnes/npm-run-all2/pull/141)
- Upgrade: Bump p-queue from 7.4.1 to 8.0.1 [`#138`](https://github.com/bcomnes/npm-run-all2/pull/138)

## [v6.2.0](https://github.com/bcomnes/npm-run-all2/compare/v6.1.2...v6.2.0) - 2024-05-17

### Merged

- Placeholder that unfolds into multiple tasks [`#134`](https://github.com/bcomnes/npm-run-all2/pull/134)
- 📝 add compatibility note for pnpm. [`#136`](https://github.com/bcomnes/npm-run-all2/pull/136)
- Upgrade: Bump codecov/codecov-action from 3 to 4 [`#131`](https://github.com/bcomnes/npm-run-all2/pull/131)

## [v6.1.2](https://github.com/bcomnes/npm-run-all2/compare/v6.1.1...v6.1.2) - 2024-01-31

### Merged

- feat: move to read-package-json-fast [`#130`](https://github.com/bcomnes/npm-run-all2/pull/130)
- Upgrade: Bump c8 from 8.0.1 to 9.0.0 [`#127`](https://github.com/bcomnes/npm-run-all2/pull/127)
- Upgrade: Bump github/codeql-action from 2 to 3 [`#126`](https://github.com/bcomnes/npm-run-all2/pull/126)
- Upgrade: Bump actions/setup-node from 3 to 4 [`#123`](https://github.com/bcomnes/npm-run-all2/pull/123)

### Commits

- Publish the whole project [`3dde20c`](https://github.com/bcomnes/npm-run-all2/commit/3dde20c1c8fa973045773e03f4fc121360fdbed4)
- Utilize CJS require for 'read-package-json-fast' [`605ca15`](https://github.com/bcomnes/npm-run-all2/commit/605ca15d9adee3ce14da6fcaa98cb14d9c03795c)
- Update FUNDING.yml [`c838ee9`](https://github.com/bcomnes/npm-run-all2/commit/c838ee9eea06e545d1a7f25592f7beb8468f1afd)

## [v6.1.1](https://github.com/bcomnes/npm-run-all2/compare/v6.1.0...v6.1.1) - 2023-10-04

### Commits

- Add an npm-run-all2 bin alias [`e6dc017`](https://github.com/bcomnes/npm-run-all2/commit/e6dc0175006a9a703c1256949f8424922043a33a)
- Fix npx on node 16 [`cfbd974`](https://github.com/bcomnes/npm-run-all2/commit/cfbd974a5990e8d549ae8bf7bfb632424ff4990b)

## [v6.1.0](https://github.com/bcomnes/npm-run-all2/compare/v6.0.6...v6.1.0) - 2023-10-04

### Merged

- Upgrade: Bump actions/checkout from 3 to 4 [`#119`](https://github.com/bcomnes/npm-run-all2/pull/119)

### Commits

- Lets avoid spawn.sync [`a3ee6cd`](https://github.com/bcomnes/npm-run-all2/commit/a3ee6cd9e051471bfd7b1b4d153aa260fc9b6634)
- Add support for pnpm (#117) [`3df3708`](https://github.com/bcomnes/npm-run-all2/commit/3df37084ab1ae55f873fcbb449ad0d7df8bc328f)

## [v6.0.6](https://github.com/bcomnes/npm-run-all2/compare/v6.0.5...v6.0.6) - 2023-07-04

### Merged

- Update all esm only packages [`#114`](https://github.com/bcomnes/npm-run-all2/pull/114)
- Upgrade: Bump c8 from 7.14.0 to 8.0.0 [`#111`](https://github.com/bcomnes/npm-run-all2/pull/111)
- Delete .nycrc [`#109`](https://github.com/bcomnes/npm-run-all2/pull/109)
- Update CodeQL workflow [`#110`](https://github.com/bcomnes/npm-run-all2/pull/110)

### Commits

- Lint fix and a few hand fixes [`2c81236`](https://github.com/bcomnes/npm-run-all2/commit/2c8123694b73084f37b68eb6719632024331d2e9)
- Fix tests [`79e2c97`](https://github.com/bcomnes/npm-run-all2/commit/79e2c97c5b32c46d5cf64ce37b3b78cf4035498e)
- Update p-queue and ansi-styles [`10b075c`](https://github.com/bcomnes/npm-run-all2/commit/10b075c849153822e9abc1447222d186a1cd6136)

## [v6.0.5](https://github.com/bcomnes/npm-run-all2/compare/v6.0.4...v6.0.5) - 2023-04-03

### Merged

- Upgrade: Bump bcomnes/npm-bump from 2.1.0 to 2.2.1 [`#104`](https://github.com/bcomnes/npm-run-all2/pull/104)
- Upgrade: Bump minimatch from 6.2.0 to 7.0.0 [`#103`](https://github.com/bcomnes/npm-run-all2/pull/103)
- Upgrade: Bump minimatch from 5.1.4 to 6.0.4 [`#102`](https://github.com/bcomnes/npm-run-all2/pull/102)
- Upgrade: Bump fs-extra from 10.1.0 to 11.1.0 [`#98`](https://github.com/bcomnes/npm-run-all2/pull/98)

### Commits

- Merge pull request #105 from bcomnes/dependabot/npm_and_yarn/minimatch-8.0.2 [`cbf78c8`](https://github.com/bcomnes/npm-run-all2/commit/cbf78c8155365db9ec06cb8054bc821e057d06e2)
- Upgrade: Bump minimatch from 7.4.4 to 8.0.2 [`c90d02b`](https://github.com/bcomnes/npm-run-all2/commit/c90d02b02df6dd33cbab01caac44b9729e012bb9)
- Merge pull request #101 from bcomnes/dependabot/npm_and_yarn/rimraf-4.0.4 [`d0d46a2`](https://github.com/bcomnes/npm-run-all2/commit/d0d46a2b0aa87a3c0c79b78a013415e7902c8324)

## [v6.0.4](https://github.com/bcomnes/npm-run-all2/compare/v6.0.3...v6.0.4) - 2022-11-09

### Merged

- When running through npx, use the npm that should be next to it. [`#96`](https://github.com/bcomnes/npm-run-all2/pull/96)

## [v6.0.3](https://github.com/bcomnes/npm-run-all2/compare/v6.0.2...v6.0.3) - 2022-11-09

### Merged

- Upgrade: Bump jsdoc from 3.6.11 to 4.0.0 [`#95`](https://github.com/bcomnes/npm-run-all2/pull/95)
- Upgrade: Bump bcomnes/npm-bump from 2.0.2 to 2.1.0 [`#92`](https://github.com/bcomnes/npm-run-all2/pull/92)
- docs: update minimum supported Node version [`#90`](https://github.com/bcomnes/npm-run-all2/pull/90)

### Commits

- Merge pull request #94 from MarmadileManteater/runjs-being-called-instead-of-npm-run [`da913f9`](https://github.com/bcomnes/npm-run-all2/commit/da913f9481543907457bd2298ad17192a4420874)
- Use NPM_CLI_JS over npm_execpath [`0224167`](https://github.com/bcomnes/npm-run-all2/commit/022416740f0d9cf8eae2f2e4ca4de8d09a6b67d8)
- Add a proper check for yarn [`bb41ef6`](https://github.com/bcomnes/npm-run-all2/commit/bb41ef6fd85a803a4a22e8382f67ea9e3e235b7d)

## [v6.0.2](https://github.com/bcomnes/npm-run-all2/compare/v6.0.1...v6.0.2) - 2022-08-16

### Merged

- Update package shell quote [`#89`](https://github.com/bcomnes/npm-run-all2/pull/89)

## [v6.0.1](https://github.com/bcomnes/npm-run-all2/compare/v6.0.0...v6.0.1) - 2022-06-14

### Commits

- Lower bound node engine to ^14.18.0 || &gt;=16.0.0 [`fc2957f`](https://github.com/bcomnes/npm-run-all2/commit/fc2957f4814848b55bc29b0a0a1def8bfadda18b)

## [v6.0.0](https://github.com/bcomnes/npm-run-all2/compare/v5.0.2...v6.0.0) - 2022-06-11

### Merged

- Move support to node 16 and npm 8 [`#85`](https://github.com/bcomnes/npm-run-all2/pull/85)
- Upgrade: Bump pidtree from 0.5.0 to 0.6.0 [`#84`](https://github.com/bcomnes/npm-run-all2/pull/84)
- Upgrade: Bump mocha from 9.2.2 to 10.0.0 [`#83`](https://github.com/bcomnes/npm-run-all2/pull/83)
- Upgrade: Bump github/codeql-action from 1 to 2 [`#82`](https://github.com/bcomnes/npm-run-all2/pull/82)
- Upgrade: Bump fastify/github-action-merge-dependabot from 3.0.2 to 3.1 [`#78`](https://github.com/bcomnes/npm-run-all2/pull/78)
- Upgrade: Bump codecov/codecov-action from 2 to 3 [`#77`](https://github.com/bcomnes/npm-run-all2/pull/77)
- Upgrade: Bump actions/setup-node from 2 to 3 [`#75`](https://github.com/bcomnes/npm-run-all2/pull/75)
- Upgrade: Bump actions/checkout from 2 to 3 [`#76`](https://github.com/bcomnes/npm-run-all2/pull/76)
- Upgrade: Bump minimatch from 4.2.1 to 5.0.0 [`#74`](https://github.com/bcomnes/npm-run-all2/pull/74)
- Upgrade: Bump minimatch from 3.1.1 to 4.1.1 [`#73`](https://github.com/bcomnes/npm-run-all2/pull/73)
- Upgrade: Bump fastify/github-action-merge-dependabot from 2.7.1 to 3.0.2 [`#72`](https://github.com/bcomnes/npm-run-all2/pull/72)
- Upgrade: Bump fastify/github-action-merge-dependabot from 2.7.0 to 2.7.1 [`#71`](https://github.com/bcomnes/npm-run-all2/pull/71)
- Upgrade: Bump fastify/github-action-merge-dependabot from 2.6.0 to 2.7.0 [`#70`](https://github.com/bcomnes/npm-run-all2/pull/70)
- Upgrade: Bump fastify/github-action-merge-dependabot from 2.5.0 to 2.6.0 [`#69`](https://github.com/bcomnes/npm-run-all2/pull/69)
- Simplify npm scripts [`#64`](https://github.com/bcomnes/npm-run-all2/pull/64)
- Update CI config [`#62`](https://github.com/bcomnes/npm-run-all2/pull/62)
- Add CodeQL workflow [`#65`](https://github.com/bcomnes/npm-run-all2/pull/65)
- Switch to c8 for coverage [`#66`](https://github.com/bcomnes/npm-run-all2/pull/66)
- tests: switch to assert's strict mode [`#67`](https://github.com/bcomnes/npm-run-all2/pull/67)
- Enforce LF in the repo. [`#61`](https://github.com/bcomnes/npm-run-all2/pull/61)
- Upgrade: Bump actions/setup-node from 2.4.0 to 2.4.1 [`#59`](https://github.com/bcomnes/npm-run-all2/pull/59)
- Upgrade: Bump fastify/github-action-merge-dependabot from 2.4.0 to 2.5.0 [`#58`](https://github.com/bcomnes/npm-run-all2/pull/58)
- Upgrade: Bump codecov/codecov-action from 2.0.2 to 2.1.0 [`#57`](https://github.com/bcomnes/npm-run-all2/pull/57)
- Upgrade: Bump fastify/github-action-merge-dependabot from 2.2.0 to 2.4.0 [`#54`](https://github.com/bcomnes/npm-run-all2/pull/54)
- Upgrade: Bump actions/setup-node from 2.3.2 to 2.4.0 [`#53`](https://github.com/bcomnes/npm-run-all2/pull/53)
- Upgrade: Bump actions/setup-node from 2.3.1 to 2.3.2 [`#52`](https://github.com/bcomnes/npm-run-all2/pull/52)
- Upgrade: Bump actions/setup-node from 2.3.0 to 2.3.1 [`#51`](https://github.com/bcomnes/npm-run-all2/pull/51)
- Upgrade: Bump codecov/codecov-action from 2.0.1 to 2.0.2 [`#50`](https://github.com/bcomnes/npm-run-all2/pull/50)
- Upgrade: Bump actions/setup-node from 2.2.0 to 2.3.0 [`#49`](https://github.com/bcomnes/npm-run-all2/pull/49)
- Upgrade: Bump codecov/codecov-action from 1.5.2 to 2.0.1 [`#48`](https://github.com/bcomnes/npm-run-all2/pull/48)
- Upgrade: Bump fastify/github-action-merge-dependabot from 2.1.1 to 2.2.0 [`#47`](https://github.com/bcomnes/npm-run-all2/pull/47)
- Upgrade: Bump actions/setup-node from 2.1.5 to 2.2.0 [`#46`](https://github.com/bcomnes/npm-run-all2/pull/46)
- Upgrade: Bump codecov/codecov-action from 1.5.0 to 1.5.2 [`#44`](https://github.com/bcomnes/npm-run-all2/pull/44)
- Upgrade: Bump mocha from 8.4.0 to 9.0.0 [`#43`](https://github.com/bcomnes/npm-run-all2/pull/43)
- Upgrade: Bump fastify/github-action-merge-dependabot from 2.1.0 to 2.1.1 [`#42`](https://github.com/bcomnes/npm-run-all2/pull/42)
- Upgrade: Bump fastify/github-action-merge-dependabot from 2.0.0 to 2.1.0 [`#41`](https://github.com/bcomnes/npm-run-all2/pull/41)
- Upgrade: Bump gh-release from 5.0.2 to 6.0.0 [`#40`](https://github.com/bcomnes/npm-run-all2/pull/40)
- Upgrade: Bump codecov/codecov-action from 1 to 1.5.0 [`#39`](https://github.com/bcomnes/npm-run-all2/pull/39)
- Upgrade: Bump fs-extra from 9.1.0 to 10.0.0 [`#38`](https://github.com/bcomnes/npm-run-all2/pull/38)
- Upgrade: Bump fastify/github-action-merge-dependabot from v1.2.1 to v2.0.0 [`#33`](https://github.com/bcomnes/npm-run-all2/pull/33)
- Upgrade: Bump fastify/github-action-merge-dependabot [`#32`](https://github.com/bcomnes/npm-run-all2/pull/32)
- Upgrade: Bump fastify/github-action-merge-dependabot from v1.1.1 to v1.2.0 [`#31`](https://github.com/bcomnes/npm-run-all2/pull/31)
- Upgrade: Bump actions/setup-node from v2.1.4 to v2.1.5 [`#30`](https://github.com/bcomnes/npm-run-all2/pull/30)
- Upgrade: Bump gh-release from 4.0.4 to 5.0.0 [`#29`](https://github.com/bcomnes/npm-run-all2/pull/29)
- Upgrade: Bump actions/setup-node from v2.1.3 to v2.1.4 [`#28`](https://github.com/bcomnes/npm-run-all2/pull/28)
- Upgrade: Bump actions/setup-node from v2.1.2 to v2.1.3 [`#27`](https://github.com/bcomnes/npm-run-all2/pull/27)

### Fixed

- Disable override tests on &gt; npm 7 [`#79`](https://github.com/bcomnes/npm-run-all2/issues/79)

### Commits

- **Breaking change:** Bump engines to node 16 and npm 8 [`7d19dd4`](https://github.com/bcomnes/npm-run-all2/commit/7d19dd47ee70286878f380934d18823310355471)
- Add auto merge [`e598066`](https://github.com/bcomnes/npm-run-all2/commit/e598066fea7478e0fce14b4f09d64fdf37b0420f)
- Update test.yml [`96260d6`](https://github.com/bcomnes/npm-run-all2/commit/96260d6c088ce0aa2bd367ff0736d653f5b0b1f1)

## [v5.0.2](https://github.com/bcomnes/npm-run-all2/compare/v5.0.1...v5.0.2) - 2020-12-08

### Merged

- Upgrade: Bump ansi-styles from 4.3.0 to 5.0.0 [`#26`](https://github.com/bcomnes/npm-run-all2/pull/26)
- Upgrade: Bump actions/checkout from v2.3.3 to v2.3.4 [`#25`](https://github.com/bcomnes/npm-run-all2/pull/25)

## [v5.0.1](https://github.com/bcomnes/npm-run-all2/compare/v5.0.0...v5.0.1) - 2020-10-24

### Commits

- Fix repo field to a valid format [`00b88f8`](https://github.com/bcomnes/npm-run-all2/commit/00b88f8a399d45cb104a33357cf56015ab92a1c0)
- Remove duplicate repo field [`a2d11ff`](https://github.com/bcomnes/npm-run-all2/commit/a2d11ff3f234812ba660be32f3a9a0aa45a510f6)
- Update FUNDING.yml [`648a541`](https://github.com/bcomnes/npm-run-all2/commit/648a5418725b4330571e08e9e1300756c98edd76)

## [v5.0.0](https://github.com/bcomnes/npm-run-all2/compare/v4.1.5...v5.0.0) - 2020-10-04

### Merged

- report codecov [`#21`](https://github.com/bcomnes/npm-run-all2/pull/21)
- Use built in string.padEnd [`#22`](https://github.com/bcomnes/npm-run-all2/pull/22)
- Upgrade: Bump fs-extra from 7.0.1 to 9.0.1 [`#17`](https://github.com/bcomnes/npm-run-all2/pull/17)
- remove @types/node [`#20`](https://github.com/bcomnes/npm-run-all2/pull/20)
- Upgrade: Bump @types/node from 4.9.4 to 14.11.2 [`#18`](https://github.com/bcomnes/npm-run-all2/pull/18)
- remove dependency on chalk [`#19`](https://github.com/bcomnes/npm-run-all2/pull/19)
- Upgrade: Bump eslint-config-mysticatea from 12.0.0 to 13.0.2 [`#11`](https://github.com/bcomnes/npm-run-all2/pull/11)
- Upgrade: Bump rimraf from 2.7.1 to 3.0.2 [`#10`](https://github.com/bcomnes/npm-run-all2/pull/10)
- Upgrade: Bump ansi-styles from 3.2.1 to 4.2.1 [`#12`](https://github.com/bcomnes/npm-run-all2/pull/12)
- Upgrade: Bump cross-spawn from 6.0.5 to 7.0.3 [`#9`](https://github.com/bcomnes/npm-run-all2/pull/9)
- Enable windows testing [`#15`](https://github.com/bcomnes/npm-run-all2/pull/15)
- Upgrade: Bump babel-preset-power-assert from 2.0.0 to 3.0.0 [`#3`](https://github.com/bcomnes/npm-run-all2/pull/3)
- more renaming [`#13`](https://github.com/bcomnes/npm-run-all2/pull/13)
- Upgrade: Bump p-queue from 2.4.2 to 6.6.1 [`#4`](https://github.com/bcomnes/npm-run-all2/pull/4)
- Upgrade: Bump nyc from 11.9.0 to 15.1.0 [`#7`](https://github.com/bcomnes/npm-run-all2/pull/7)
- Upgrade: Bump pidtree from 0.3.1 to 0.5.0 [`#5`](https://github.com/bcomnes/npm-run-all2/pull/5)
- Upgrade: Bump read-pkg from 3.0.0 to 5.2.0 [`#6`](https://github.com/bcomnes/npm-run-all2/pull/6)
- Upgrade: Bump mocha from 5.2.0 to 8.1.3 [`#8`](https://github.com/bcomnes/npm-run-all2/pull/8)
- Upgrade: Bump actions/setup-node from v2.1.1 to v2.1.2 [`#1`](https://github.com/bcomnes/npm-run-all2/pull/1)
- Upgrade: Bump actions/checkout from v2.3.2 to v2.3.3 [`#2`](https://github.com/bcomnes/npm-run-all2/pull/2)
- 🐛 fix signal handling [`#171`](https://github.com/bcomnes/npm-run-all2/pull/171)

### Commits

- fix lint [`acce8f7`](https://github.com/bcomnes/npm-run-all2/commit/acce8f78b0a4ac656020c14ab946d57639e22764)
- ⚒ switch to actions and use dependabot [`13696f8`](https://github.com/bcomnes/npm-run-all2/commit/13696f8aef475fb66346a8b219c9f07d026d2513)
- Fix lint [`87b3e01`](https://github.com/bcomnes/npm-run-all2/commit/87b3e01c1405bb92c39a1aecdf957805bcc184c0)

## [v4.1.5](https://github.com/bcomnes/npm-run-all2/compare/v4.1.4...v4.1.5) - 2018-11-24

### Commits

- 🐛 use pidtree [`1b41ac5`](https://github.com/bcomnes/npm-run-all2/commit/1b41ac569987c96e224f940ff59f9699322c7824)
- ⬆️ update dependencies [`7ec542e`](https://github.com/bcomnes/npm-run-all2/commit/7ec542e95ceb922b9abe593270d9b6f8e0df4bc5)
- 🔖 4.1.5 [`df15118`](https://github.com/bcomnes/npm-run-all2/commit/df1511851a2b5e8a406e4a2622829b360f671afc)

## [v4.1.4](https://github.com/bcomnes/npm-run-all2/compare/v4.1.3...v4.1.4) - 2018-11-24

### Commits

- 🔥 remove ps-tree [`57d72eb`](https://github.com/bcomnes/npm-run-all2/commit/57d72eb98c2ce108f07d2a2cf1b44d57f08ec3ca)
- 🔖 4.1.4 [`a79fbac`](https://github.com/bcomnes/npm-run-all2/commit/a79fbacda181ec8a240d8bf8d34f9303da3c1fd1)
- remove test in version script temporary [`d97929c`](https://github.com/bcomnes/npm-run-all2/commit/d97929cdd73c615e053202c2a5068eb0cf1af08e)

## [v4.1.3](https://github.com/bcomnes/npm-run-all2/compare/v4.1.2...v4.1.3) - 2018-05-07

### Merged

- Fix: unexpected behavior of leading bang [`#124`](https://github.com/bcomnes/npm-run-all2/pull/124)
- Docs: typo fix in README.md: is/are [`#120`](https://github.com/bcomnes/npm-run-all2/pull/120)
- Fix: colorize tasks names sequentially instead of by hash [`#115`](https://github.com/bcomnes/npm-run-all2/pull/115)

### Commits

- Chore: trivial fix [`21c0269`](https://github.com/bcomnes/npm-run-all2/commit/21c02697e59748c0d2ac55b5244bd0ca10ecbfcc)
- Chore: upgrade dependencies [`61bad5c`](https://github.com/bcomnes/npm-run-all2/commit/61bad5c72678f35f8dd1dcd68eb20b688795ffd5)

## [v4.1.2](https://github.com/bcomnes/npm-run-all2/compare/v4.1.1...v4.1.2) - 2017-11-07

### Fixed

- Fix: `--aggregate-output` cannot handle large data (fixes #111)(#112) [`#111`](https://github.com/bcomnes/npm-run-all2/issues/111)

### Commits

- Chore: fix tests [`43a6b16`](https://github.com/bcomnes/npm-run-all2/commit/43a6b1683374e3dd314733ec5559908abfcc6cd1)
- Fix: fix for latest yarn [`dfb9dcb`](https://github.com/bcomnes/npm-run-all2/commit/dfb9dcb40dc0d309480e28dbdaac24878e4c32c6)
- Fix: missing --aggregate-output in npm-run-all [`693261b`](https://github.com/bcomnes/npm-run-all2/commit/693261b6169071b61b443eec5ad6d04525394500)

## [v4.1.1](https://github.com/bcomnes/npm-run-all2/compare/v4.1.0...v4.1.1) - 2017-08-28

### Merged

- Fix: Use ansi-style directly instead of chalk.styles [`#108`](https://github.com/bcomnes/npm-run-all2/pull/108)

### Commits

- Chore: configure appveyor to run on only Node 8. [`ac9358a`](https://github.com/bcomnes/npm-run-all2/commit/ac9358a669611cedb7eb7201e770d60886347ca5)

## [v4.1.0](https://github.com/bcomnes/npm-run-all2/compare/v4.0.2...v4.1.0) - 2017-08-26

### Merged

- Docs: add note about Yarn compatibility [`#103`](https://github.com/bcomnes/npm-run-all2/pull/103)
- Add Node.js 8.x to Travis CI [`#101`](https://github.com/bcomnes/npm-run-all2/pull/101)
- Include docs in published npm package. [`#95`](https://github.com/bcomnes/npm-run-all2/pull/95)

### Fixed

- Fix: MaxListenersExceededWarning (fixes #105) [`#105`](https://github.com/bcomnes/npm-run-all2/issues/105)
- New: add `--aggregate-output` option (fixes #36)(#104) [`#36`](https://github.com/bcomnes/npm-run-all2/issues/36)

### Commits

- Chore: use async/await in tests [`64f6479`](https://github.com/bcomnes/npm-run-all2/commit/64f647988007d36b16c96a700c6c06fdb8175066)
- Docs: update docs [`8c8bec0`](https://github.com/bcomnes/npm-run-all2/commit/8c8bec0701586c2a0169ef5f601683e61837e901)
- Chore: tweak CI settings [`8c6debf`](https://github.com/bcomnes/npm-run-all2/commit/8c6debfc5b8eac43f21bdc9e2a3d163927e59749)

## [v4.0.2](https://github.com/bcomnes/npm-run-all2/compare/v4.0.1...v4.0.2) - 2017-02-23

### Fixed

- Fix: it threw for --race even if --parallel exists (fixes #88) [`#88`](https://github.com/bcomnes/npm-run-all2/issues/88)

## [v4.0.1](https://github.com/bcomnes/npm-run-all2/compare/v4.0.0...v4.0.1) - 2017-01-17

### Merged

- Simplify repository config [`#82`](https://github.com/bcomnes/npm-run-all2/pull/82)
- Fix Case Sensitive NPM_EXECPATH [`#84`](https://github.com/bcomnes/npm-run-all2/pull/84)
- Fixed: typo in `help` CLI command. [`#83`](https://github.com/bcomnes/npm-run-all2/pull/83)

### Commits

- NPM_EXECPATH -&gt; npm_execpath, this variable is case sensitive [`bb9e627`](https://github.com/bcomnes/npm-run-all2/commit/bb9e627406fd98933e237358c731ea74b5fd1634)
- Docs: update README.md [`6beda60`](https://github.com/bcomnes/npm-run-all2/commit/6beda6057d5bdda5da7ba370597468fb618c9ab3)
- Chore: switch to codecov [`378c54b`](https://github.com/bcomnes/npm-run-all2/commit/378c54b549edc7e9bb26f0c2f86f83a28cddad50)

## [v4.0.0](https://github.com/bcomnes/npm-run-all2/compare/v3.1.2...v4.0.0) - 2017-01-01

### Merged

- Change labels to be displayed in individual colors for each task [`#75`](https://github.com/bcomnes/npm-run-all2/pull/75)

### Fixed

- Merge pull request #75 from nulltask/feature/colorize (fixes #73) [`#73`](https://github.com/bcomnes/npm-run-all2/issues/73)

### Commits

- Chore: upgrade eslint and eslint-config [`3f933c3`](https://github.com/bcomnes/npm-run-all2/commit/3f933c326567da1ba1530fbb5d9047fc31d325c9)
- New: --max-parallel option [`02e9767`](https://github.com/bcomnes/npm-run-all2/commit/02e97675e3852aefd758d40fef366dc3be5b7aad)
- Breaking: use NPM_EXECPATH to run tasks [`12b2b87`](https://github.com/bcomnes/npm-run-all2/commit/12b2b87cf473e4b11e27da64b4c27d9f2db8d7e6)

## [v3.1.2](https://github.com/bcomnes/npm-run-all2/compare/v3.1.1...v3.1.2) - 2016-12-01

### Fixed

- Fix: remove useless `if` for old tests (fixes #67) [`#67`](https://github.com/bcomnes/npm-run-all2/issues/67)
- Fix: --version command was wrong (fixes #70) [`#70`](https://github.com/bcomnes/npm-run-all2/issues/70)

### Commits

- Chore: improve tests [`4b26037`](https://github.com/bcomnes/npm-run-all2/commit/4b260379beeb8757a79e94e3f4e678008bf1bd6f)
- Chore: update travis.yml with dot reporter. [`413a7d5`](https://github.com/bcomnes/npm-run-all2/commit/413a7d52fd4d12121ddba265e4af4c6b56fdc4dd)
- Chore: add @types/node [`76c7f3d`](https://github.com/bcomnes/npm-run-all2/commit/76c7f3d989dd0d8ae331e7b1fddcd5c374895335)

## [v3.1.1](https://github.com/bcomnes/npm-run-all2/compare/v3.1.0...v3.1.1) - 2016-10-15

### Merged

- fix: use run command instead of run-script [`#66`](https://github.com/bcomnes/npm-run-all2/pull/66)
- docs: add hint about forcing chalk coloring [`#65`](https://github.com/bcomnes/npm-run-all2/pull/65)

## [v3.1.0](https://github.com/bcomnes/npm-run-all2/compare/v3.0.0...v3.1.0) - 2016-09-01

### Fixed

- New: add supporting `$npm_config_xxx` (fixes #60) [`#60`](https://github.com/bcomnes/npm-run-all2/issues/60)

### Commits

- Merge pull request #61 from mysticatea/issue60 [`6a3697d`](https://github.com/bcomnes/npm-run-all2/commit/6a3697dcea11a9a461660f097662bdf131209dbd)
- Chore: rename tests: env-check/overwriting → package-config [`a32ca2f`](https://github.com/bcomnes/npm-run-all2/commit/a32ca2f1f4749424b2947f6daa12a723b0604721)

## [v3.0.0](https://github.com/bcomnes/npm-run-all2/compare/v2.3.0...v3.0.0) - 2016-08-17

### Merged

- Added appveyor support for modern node versions [`#57`](https://github.com/bcomnes/npm-run-all2/pull/57)

### Fixed

- Breaking: supports default values for argument placeholders (fixes #54) [`#54`](https://github.com/bcomnes/npm-run-all2/issues/54)

### Commits

- Chore: improve --race tests [`58c5df8`](https://github.com/bcomnes/npm-run-all2/commit/58c5df875908d5641e39ac2ac762dc063fdbcd94)
- Chore: try to improve tests for --race [`2e5663a`](https://github.com/bcomnes/npm-run-all2/commit/2e5663ac62739a602ab4703bfd7690c223117f6b)
- Docs: update README.md [`1e2b299`](https://github.com/bcomnes/npm-run-all2/commit/1e2b29973e854fb766d38bfab4f49c8079373605)

## [v2.3.0](https://github.com/bcomnes/npm-run-all2/compare/v2.2.2...v2.3.0) - 2016-06-28

### Fixed

- New: race option (fixes #42) [`#42`](https://github.com/bcomnes/npm-run-all2/issues/42)

### Commits

- Chore: upgrade my ESLint config. [`b740e19`](https://github.com/bcomnes/npm-run-all2/commit/b740e193c856e6f77bc8ecaeb3082dffb2fca3cf)

## [v2.2.2](https://github.com/bcomnes/npm-run-all2/compare/v2.2.1...v2.2.2) - 2016-06-23

### Commits

- Upgrade: shell-quote 1.6.1 [`e7ef8cf`](https://github.com/bcomnes/npm-run-all2/commit/e7ef8cffef04fb393c7bc51d9f82c3aed3fdefa0)

## [v2.2.1](https://github.com/bcomnes/npm-run-all2/compare/v2.2.0...v2.2.1) - 2016-06-23

### Commits

- Upgrade: minimatch 3.0.2 [`5dc5056`](https://github.com/bcomnes/npm-run-all2/commit/5dc5056210bda994f8ba857857a04f6dbf737cf5)

## [v2.2.0](https://github.com/bcomnes/npm-run-all2/compare/v2.1.2...v2.2.0) - 2016-06-18

### Merged

- Docs: ESLint use `--color` not `--colors` [`#47`](https://github.com/bcomnes/npm-run-all2/pull/47)

### Fixed

- New: add a feature to pass arguments on to each task. (fixes #44) [`#44`](https://github.com/bcomnes/npm-run-all2/issues/44)

### Commits

- Fix: add tests for argument-passthrough [`e18ce01`](https://github.com/bcomnes/npm-run-all2/commit/e18ce01e77efef3e57e8467d29e086a037c40d76)
- Docs: add documents about argument placeholders. [`c9f69b4`](https://github.com/bcomnes/npm-run-all2/commit/c9f69b429c1b718f9e1c72c7dbcceb82b021f0cf)
- Fix: refactor and change Node API for argument-passthrough [`49c9e67`](https://github.com/bcomnes/npm-run-all2/commit/49c9e67daf48ddbc87c9de8efba1254fd6752ed4)

## [v2.1.2](https://github.com/bcomnes/npm-run-all2/compare/v2.1.1...v2.1.2) - 2016-06-13

### Commits

- Upgrade: cross-spawn 4.0.0 [`16603c3`](https://github.com/bcomnes/npm-run-all2/commit/16603c3db0747c063ba58f9b0d68206ef0cbc469)

## [v2.1.1](https://github.com/bcomnes/npm-run-all2/compare/v2.1.0...v2.1.1) - 2016-05-19

### Fixed

- Upgrade: `cross-spawn-async` to `cross-spawn` (fixes #40) [`#40`](https://github.com/bcomnes/npm-run-all2/issues/40)

## [v2.1.0](https://github.com/bcomnes/npm-run-all2/compare/v2.0.0...v2.1.0) - 2016-05-13

### Commits

- Update: `results` of Node API [`f4ef9f7`](https://github.com/bcomnes/npm-run-all2/commit/f4ef9f7bef8c443eb04a5289f7d08795fed45faf)
- Fix: invalid jsdoc comments. [`32daa0e`](https://github.com/bcomnes/npm-run-all2/commit/32daa0ec98c85e8326a9608fc5c404cb70bb47f8)
- Fix: results of aborted tasks on linux [`4a1bd16`](https://github.com/bcomnes/npm-run-all2/commit/4a1bd169e760c84a614a871351745ab13e37630f)

## [v2.0.0](https://github.com/bcomnes/npm-run-all2/compare/v1.8.0...v2.0.0) - 2016-05-11

### Merged

- Build: Add Node 6 to Travis CI [`#39`](https://github.com/bcomnes/npm-run-all2/pull/39)

### Commits

- Update: add tests for shorthand commands. [`4496065`](https://github.com/bcomnes/npm-run-all2/commit/4496065f16bf13c22a78f8f1b49d1e606f75af2e)
- Add `run-s` and `run-p` commands [`40554ef`](https://github.com/bcomnes/npm-run-all2/commit/40554ef517781856169c70adc574f7719252e3ee)
- Chore: Stop a use of ES6 modules. And upgrade eslint. [`2f26fa5`](https://github.com/bcomnes/npm-run-all2/commit/2f26fa5e21e07a74a1a5e56c224dff1ed361c63a)

## [v1.8.0](https://github.com/bcomnes/npm-run-all2/compare/v1.7.0...v1.8.0) - 2016-04-24

### Fixed

- New: `--print-name` option (fixes #35) [`#35`](https://github.com/bcomnes/npm-run-all2/issues/35)
- New: `--print-label` option (fixes #23) [`#23`](https://github.com/bcomnes/npm-run-all2/issues/23)

## [v1.7.0](https://github.com/bcomnes/npm-run-all2/compare/v1.6.0...v1.7.0) - 2016-03-30

### Fixed

- Update: `--continue-on-error` option (fixes #34) [`#34`](https://github.com/bcomnes/npm-run-all2/issues/34)

## [v1.6.0](https://github.com/bcomnes/npm-run-all2/compare/v1.5.3...v1.6.0) - 2016-03-20

### Merged

- added --serial option as a synonym for sequential [`#31`](https://github.com/bcomnes/npm-run-all2/pull/31)

### Fixed

- added --serial option as a synonym for sequential [`#30`](https://github.com/bcomnes/npm-run-all2/issues/30)

### Commits

- Docs: fix help text within 80 columns. [`b1e6c5a`](https://github.com/bcomnes/npm-run-all2/commit/b1e6c5a1a487e3054fb24f564a8bab9b02b5348a)

## [v1.5.3](https://github.com/bcomnes/npm-run-all2/compare/v1.5.2...v1.5.3) - 2016-03-13

### Fixed

- Fix: Add a workaround for Git Bash for Windows (fixes #24) [`#24`](https://github.com/bcomnes/npm-run-all2/issues/24)

### Commits

- Build: Update appveyor.yml [`6585c66`](https://github.com/bcomnes/npm-run-all2/commit/6585c664827de1c2bbedc7b4519c2454d0072e2a)

## [v1.5.2](https://github.com/bcomnes/npm-run-all2/compare/v1.5.1...v1.5.2) - 2016-03-08

### Fixed

- Fix: using `cross-spawn-async` for spaced paths (fixes #26, fixes #27) [`#26`](https://github.com/bcomnes/npm-run-all2/issues/26) [`#27`](https://github.com/bcomnes/npm-run-all2/issues/27)

### Commits

- Add: appveyor and badge to readme [`b1ac307`](https://github.com/bcomnes/npm-run-all2/commit/b1ac30763a622ada39847c328f078f5ea685bdae)
- Fix: AppVeyor settings [`b1f1eb1`](https://github.com/bcomnes/npm-run-all2/commit/b1f1eb1cd35fc4d36c1edc3aacd57a9d24503c38)
- Fix: trivial change. [`aa2dd2b`](https://github.com/bcomnes/npm-run-all2/commit/aa2dd2b55b0a2d8cdcf2c680ded0709d1e545952)

## [v1.5.1](https://github.com/bcomnes/npm-run-all2/compare/v1.5.0...v1.5.1) - 2016-01-28

### Fixed

- Fix: came to report unmatched pattern error (fixes #19) [`#19`](https://github.com/bcomnes/npm-run-all2/issues/19)

### Commits

- Docs: trivial [`8f7736d`](https://github.com/bcomnes/npm-run-all2/commit/8f7736d7d096bf7a6396dd6c544be128b4b490ce)
- Upgrade: eslint-plugin-node@0.6.0 [`a497241`](https://github.com/bcomnes/npm-run-all2/commit/a49724146ea3853d915c7fbc4c8e5a4d84f839e1)

## [v1.5.0](https://github.com/bcomnes/npm-run-all2/compare/v1.4.0...v1.5.0) - 2016-01-17

### Commits

- Update: Add `silent` option [`87aedbf`](https://github.com/bcomnes/npm-run-all2/commit/87aedbf187f2e554e9d6654c9475f4c93cd0ba91)
- Fix: switch to babel-runtime [`bbad6e8`](https://github.com/bcomnes/npm-run-all2/commit/bbad6e80fef44efb9bcf77aa57299eb19069059e)
- Pin eslint-config [`92e48ea`](https://github.com/bcomnes/npm-run-all2/commit/92e48ea0ad2901e72813703252c166ff4a4f6e2b)

## [v1.4.0](https://github.com/bcomnes/npm-run-all2/compare/v1.3.4...v1.4.0) - 2015-12-08

### Fixed

- Add transfaring config overwritten to nested calls (fixes #13) [`#13`](https://github.com/bcomnes/npm-run-all2/issues/13)

### Commits

- trivial [`baea559`](https://github.com/bcomnes/npm-run-all2/commit/baea559cb07228f327c964775ab7672838f51712)

## [v1.3.4](https://github.com/bcomnes/npm-run-all2/compare/v1.3.3...v1.3.4) - 2015-12-06

### Merged

- Small typo + grammar fixes [`#16`](https://github.com/bcomnes/npm-run-all2/pull/16)

### Commits

- Fix: Add `import "babel-polyfill"` for node v0.10 (refs #17) [`9253ce9`](https://github.com/bcomnes/npm-run-all2/commit/9253ce9365284ef0aa6e8089aead67bc7a8be2b2)

## [v1.3.3](https://github.com/bcomnes/npm-run-all2/compare/v1.3.2...v1.3.3) - 2015-12-02

### Fixed

- Add notes about the behavior of non-zero-exit (fixes #15) [`#15`](https://github.com/bcomnes/npm-run-all2/issues/15)

### Commits

- Upgrade Babel. [`1fa5e5b`](https://github.com/bcomnes/npm-run-all2/commit/1fa5e5bdb14621eb36a26c8568b5ab3ef1cb060b)
- Fix for babel and eslint [`f72af44`](https://github.com/bcomnes/npm-run-all2/commit/f72af44a253e4e123bfb94cc54d8f3f237ef7c22)
- Modify ESLint's config. [`2b24221`](https://github.com/bcomnes/npm-run-all2/commit/2b24221807bce843a30d6b13c01c4687a7e97ad3)

## [v1.3.2](https://github.com/bcomnes/npm-run-all2/compare/v1.3.0...v1.3.2) - 2015-11-23

### Fixed

- Fix: use `ps-tree` to kill processes (fixes #14) [`#14`](https://github.com/bcomnes/npm-run-all2/issues/14)

### Commits

- Added esdoc-importpath-plugin [`611a956`](https://github.com/bcomnes/npm-run-all2/commit/611a9561fab73be7b17ac5bb1f3fd8f107f75c70)

## [v1.3.0](https://github.com/bcomnes/npm-run-all2/compare/v1.2.13...v1.3.0) - 2015-11-18

### Commits

- Refactoring! [`8004ce4`](https://github.com/bcomnes/npm-run-all2/commit/8004ce4f3039fe6e8d67c0b291ddf688d55176c2)
- Added `--:=` style options. [`08aa833`](https://github.com/bcomnes/npm-run-all2/commit/08aa833b5750b1fd8b4cde7463ef68c17297a0bf)
- Added postversion hook [`700b620`](https://github.com/bcomnes/npm-run-all2/commit/700b6203b20b8c72137d008c99e062cb7432eb67)

## [v1.2.13](https://github.com/bcomnes/npm-run-all2/compare/v1.2.12...v1.2.13) - 2015-11-05

### Fixed

- Fix: should not remove duplicate tasks (fixes #12) [`#12`](https://github.com/bcomnes/npm-run-all2/issues/12)

### Commits

- Upgrade: dependencies [`daad55f`](https://github.com/bcomnes/npm-run-all2/commit/daad55f95417243aa169313c145c7ee4dad2709c)
- Build: Add tests for Node.js 5 [`eabd8cf`](https://github.com/bcomnes/npm-run-all2/commit/eabd8cf5efaae9f188735735f2b46aac70afbd3d)

## [v1.2.12](https://github.com/bcomnes/npm-run-all2/compare/v1.2.10...v1.2.12) - 2015-10-18

### Commits

- Fix: added a support for built-in tasks [`b287965`](https://github.com/bcomnes/npm-run-all2/commit/b28796539082aa1d78f6777dcee9a94acf7b0594)
- Update eslint and config [`57c917c`](https://github.com/bcomnes/npm-run-all2/commit/57c917c865261314c9022d4628f7535990804eb0)
- Update .eslintrc [`97fdd8b`](https://github.com/bcomnes/npm-run-all2/commit/97fdd8b60365fb88de06bf1a15c425d11e056588)

## [v1.2.10](https://github.com/bcomnes/npm-run-all2/compare/v1.2.9...v1.2.10) - 2015-09-07

### Commits

- Remove aborting with SIGINT since stdin is inherited. [`84bff03`](https://github.com/bcomnes/npm-run-all2/commit/84bff0345933f6505c40a8cd56b9cf5460ae4664)

## [v1.2.9](https://github.com/bcomnes/npm-run-all2/compare/v1.2.8...v1.2.9) - 2015-09-07

### Commits

- Add missing .eslintrc for tests [`8c38912`](https://github.com/bcomnes/npm-run-all2/commit/8c3891277b17ec8d33f1b831b547fa8ad2595449)
- Update dependencies. [`53200d3`](https://github.com/bcomnes/npm-run-all2/commit/53200d3f2ab3638dfb26bbc9f3cb5831348c2310)
- Fix: a problem around using stdin in tasks (refs #9) [`f502903`](https://github.com/bcomnes/npm-run-all2/commit/f5029038b9b514ab6516ae650309bd0742a24095)

## [v1.2.8](https://github.com/bcomnes/npm-run-all2/compare/v1.2.7...v1.2.8) - 2015-08-26

## [v1.2.7](https://github.com/bcomnes/npm-run-all2/compare/v1.2.6...v1.2.7) - 2015-08-26

### Commits

- Update dependencies. [`2520765`](https://github.com/bcomnes/npm-run-all2/commit/2520765fe06032ef00654becbf6eccc13dc4d19a)
- Fix: Use inherit option (fixes: #8) [`736dee0`](https://github.com/bcomnes/npm-run-all2/commit/736dee0023630f8b9ce7b72e6ec79af49b35821d)
- Fix: ignores killed tasks. [`76d87cb`](https://github.com/bcomnes/npm-run-all2/commit/76d87cb25909fe5c1c518b943b32b50098bf8596)

## [v1.2.6](https://github.com/bcomnes/npm-run-all2/compare/v1.2.5...v1.2.6) - 2015-06-20

### Commits

- Kill all tasks when one of tasks exited with errors. [`87e7864`](https://github.com/bcomnes/npm-run-all2/commit/87e7864f92236218651db836b07334736dd43395)
- Update dependencies [`cf60bdf`](https://github.com/bcomnes/npm-run-all2/commit/cf60bdf09f7f04e65a4d928d2f100601f2c1bf8b)

## [v1.2.5](https://github.com/bcomnes/npm-run-all2/compare/v1.2.4...v1.2.5) - 2015-05-15

### Commits

- Update dependencies. [`448317e`](https://github.com/bcomnes/npm-run-all2/commit/448317e365d01db40950f131907773f4d4a433ef)
- Update dependencies. [`b2e2fe0`](https://github.com/bcomnes/npm-run-all2/commit/b2e2fe0b8f9c841b03743727425639efbf808915)

## [v1.2.4](https://github.com/bcomnes/npm-run-all2/compare/v1.2.3...v1.2.4) - 2015-04-26

### Commits

- Update deps and slim down npm-scripts. [`cd5c8ed`](https://github.com/bcomnes/npm-run-all2/commit/cd5c8ed1c77b8eb6651508c890486fb77b91fdc5)

## [v1.2.3](https://github.com/bcomnes/npm-run-all2/compare/v1.2.2...v1.2.3) - 2015-04-19

### Commits

- code coverage [`87a8aeb`](https://github.com/bcomnes/npm-run-all2/commit/87a8aeb865088a8012ff478855c1f650929e2381)
- Trivial fix. [`9a60535`](https://github.com/bcomnes/npm-run-all2/commit/9a60535aa21462e46962a2ef53718dd9b6ba5be5)
- Minor edits to help text [`4e8f4bd`](https://github.com/bcomnes/npm-run-all2/commit/4e8f4bd3e23ad502128bbf3ae02e8816a70fa168)

## [v1.2.2](https://github.com/bcomnes/npm-run-all2/compare/v1.2.1...v1.2.2) - 2015-04-15

### Commits

- Restore to use myself [`d41265c`](https://github.com/bcomnes/npm-run-all2/commit/d41265ceab1e1640f4ff79bbe77af691fdf4b484)

## [v1.2.1](https://github.com/bcomnes/npm-run-all2/compare/v1.2.0...v1.2.1) - 2015-04-15

### Commits

- Fix an wrong behavior [`fa42f77`](https://github.com/bcomnes/npm-run-all2/commit/fa42f772ddce0e7787231d773e9a117dfdff3612)

## [v1.2.0](https://github.com/bcomnes/npm-run-all2/compare/v1.1.3...v1.2.0) - 2015-04-15

### Commits

- New feature: glob-like task names. [`aabe1f9`](https://github.com/bcomnes/npm-run-all2/commit/aabe1f9e2f221f4592135b3a0dc9e45788175f27)
- readme improvements [`ebea491`](https://github.com/bcomnes/npm-run-all2/commit/ebea49105a89b4472bebb9b4567ce294cc391b05)
- Add about pattern-matching to README.md [`864c94f`](https://github.com/bcomnes/npm-run-all2/commit/864c94f45903e1b8f66e5d3d629bda3c577a89a3)

## [v1.1.3](https://github.com/bcomnes/npm-run-all2/compare/v1.1.2...v1.1.3) - 2015-04-12

### Commits

- Update dependencies. [`936a621`](https://github.com/bcomnes/npm-run-all2/commit/936a6210f8d60d74f3c84c77c73dd2f5696929f5)
- Adjust timing paramters... [`c362db1`](https://github.com/bcomnes/npm-run-all2/commit/c362db1a0b25784d14e6b174552f0ff25fb6cdf3)
- Fix settings for travis ci. [`c7e016a`](https://github.com/bcomnes/npm-run-all2/commit/c7e016a5010c1e57fe66b40ea4bd7eff70695fa1)

## [v1.1.2](https://github.com/bcomnes/npm-run-all2/compare/v1.1.1...v1.1.2) - 2015-04-05

### Commits

- Update dependencies. [`8a8ff02`](https://github.com/bcomnes/npm-run-all2/commit/8a8ff02aaab41dc83758a9606762634a748621e3)

## [v1.1.1](https://github.com/bcomnes/npm-run-all2/compare/v1.1.0...v1.1.1) - 2015-03-31

### Commits

- Update version of "npm-run-all" in my package.json [`db4c554`](https://github.com/bcomnes/npm-run-all2/commit/db4c5540ce97d3ce9710babf01e19614a9b46143)

## [v1.1.0](https://github.com/bcomnes/npm-run-all2/compare/v1.0.3...v1.1.0) - 2015-03-31

### Commits

- New feature: mixied running. [`0b04869`](https://github.com/bcomnes/npm-run-all2/commit/0b0486934b397349edce10246cf3f8e38acb9505)

## [v1.0.3](https://github.com/bcomnes/npm-run-all2/compare/v1.0.2...v1.0.3) - 2015-03-30

### Commits

- Update README.md [`2c19468`](https://github.com/bcomnes/npm-run-all2/commit/2c1946861fb8c2b424c9badd78896c42bbe3eab6)
- Trivial fix package.json [`4f6020c`](https://github.com/bcomnes/npm-run-all2/commit/4f6020ccb82a8362ec8839ade08ebe79bc7479f9)
- Update dependencies, and trivial update scripts. [`541e581`](https://github.com/bcomnes/npm-run-all2/commit/541e581ae2c6b8fe1541f137b203031fd83a7c9a)

## [v1.0.2](https://github.com/bcomnes/npm-run-all2/compare/v1.0.1...v1.0.2) - 2015-03-24

### Commits

- Add missing option stdin [`b6a8cae`](https://github.com/bcomnes/npm-run-all2/commit/b6a8cae59881dd3a7bf20a4c05a2ecfc0ac83524)
- Update README.md [`3c9f6e2`](https://github.com/bcomnes/npm-run-all2/commit/3c9f6e21c646dfee65902608777a34afa74c1f5b)
- Fix lint errors in test. [`9bcc9af`](https://github.com/bcomnes/npm-run-all2/commit/9bcc9af577e6a7ffb53476dc86ab10bf703b15e0)

## [v1.0.1](https://github.com/bcomnes/npm-run-all2/compare/v1.0.0...v1.0.1) - 2015-03-24

### Commits

- Use myself [`2e6ca4f`](https://github.com/bcomnes/npm-run-all2/commit/2e6ca4f190fc6d87c958d4bc2cf5e7f2e08f6787)
- Add badges [`2bab2a6`](https://github.com/bcomnes/npm-run-all2/commit/2bab2a6524abdb31119da4b243f3449b34ea7c59)

## v1.0.0 - 2015-03-24

### Commits

- Add the first version. [`b77f1a5`](https://github.com/bcomnes/npm-run-all2/commit/b77f1a5d51d62a1301c803ab95ef887c97906c48)
- Refactor [`9555a9d`](https://github.com/bcomnes/npm-run-all2/commit/9555a9dc7b9b0d7921b8f73be13a2734199f30b2)
- Create README.md [`98eca08`](https://github.com/bcomnes/npm-run-all2/commit/98eca08dc86b4ee1292cb02c05d206301964ce86)
