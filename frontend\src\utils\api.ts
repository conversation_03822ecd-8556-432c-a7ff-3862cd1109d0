/**
 * API 服务模块
 * 
 * 提供与后端 API 通信的功能。
 */

import axios, { type AxiosInstance, type AxiosResponse } from 'axios';

// API 响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  detail?: string;
}

// 语音识别结果接口
export interface SpeechRecognitionResult {
  text: string;
  begin_time: number;
  end_time: number;
  words: Array<{
    begin_time: number;
    end_time: number;
    text: string;
    punctuation?: string;
  }>;
  request_id: string;
  filename: string;
}

// 支持的格式信息接口
export interface SupportedFormats {
  supported_formats: string[];
  allowed_formats: string[];
  max_file_size_mb: number;
}

class ApiService {
  private client: AxiosInstance;

  constructor(baseURL: string = 'http://localhost:8001') {
    this.client = axios.create({
      baseURL,
      timeout: 30000, // 30秒超时
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API 请求: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API 请求错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`API 响应: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API 响应错误:', error);
        
        // 处理网络错误
        if (!error.response) {
          throw new Error('网络连接失败，请检查后端服务是否启动');
        }

        // 处理 HTTP 错误
        const { status, data } = error.response;
        const message = data?.detail || data?.message || `HTTP ${status} 错误`;
        throw new Error(message);
      }
    );
  }

  /**
   * 语音识别接口
   */
  async recognizeSpeech(audioFile: File): Promise<SpeechRecognitionResult> {
    const formData = new FormData();
    formData.append('file', audioFile);

    const response = await this.client.post<ApiResponse<SpeechRecognitionResult>>(
      '/api/speech/recognize',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    if (!response.data.success) {
      throw new Error(response.data.message || '语音识别失败');
    }

    return response.data.data!;
  }

  /**
   * 获取支持的音频格式
   */
  async getSupportedFormats(): Promise<SupportedFormats> {
    const response = await this.client.get<ApiResponse<SupportedFormats>>(
      '/api/speech/formats'
    );

    if (!response.data.success) {
      throw new Error(response.data.message || '获取支持格式失败');
    }

    return response.data.data!;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get<ApiResponse>('/api/speech/health');
      return response.data.success;
    } catch (error) {
      console.error('健康检查失败:', error);
      return false;
    }
  }

  /**
   * 获取服务信息
   */
  async getServiceInfo(): Promise<any> {
    const response = await this.client.get<ApiResponse>('/');
    
    if (!response.data.success) {
      throw new Error(response.data.message || '获取服务信息失败');
    }

    return response.data.data;
  }
}

// 创建 API 服务实例
export const apiService = new ApiService();

// 导出类型和实例
export { ApiService };
export type { ApiResponse };
