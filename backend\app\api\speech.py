"""
语音识别 API 路由

提供文件上传和语音识别的 REST API 接口。
"""

import os
import logging
from typing import Dict, Any
from pathlib import Path

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse

from ..config.settings import get_settings, UploadConfig
from ..services.speech_recognition import (
    create_speech_recognition_service, 
    SpeechRecognitionError
)


# 创建路由器
router = APIRouter(prefix="/api/speech", tags=["语音识别"])

# 日志记录器
logger = logging.getLogger(__name__)


def get_upload_config() -> UploadConfig:
    """获取上传配置"""
    return get_settings().upload


def validate_audio_file(file: UploadFile, config: UploadConfig) -> None:
    """
    验证上传的音频文件
    
    Args:
        file: 上传的文件
        config: 上传配置
        
    Raises:
        HTTPException: 文件验证失败时抛出
    """
    # 检查文件是否为空
    if not file.filename:
        raise HTTPException(status_code=400, detail="未选择文件")
    
    # 检查文件格式
    file_extension = Path(file.filename).suffix.lower().lstrip('.')
    if file_extension not in config.allowed_formats:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的文件格式: {file_extension}。支持的格式: {', '.join(config.allowed_formats)}"
        )
    
    # 检查文件大小（如果可以获取的话）
    if hasattr(file, 'size') and file.size:
        max_size_bytes = config.max_file_size * 1024 * 1024  # 转换为字节
        if file.size > max_size_bytes:
            raise HTTPException(
                status_code=400, 
                detail=f"文件大小超过限制: {config.max_file_size}MB"
            )


@router.post("/recognize", response_model=Dict[str, Any])
async def recognize_audio(
    file: UploadFile = File(..., description="音频文件"),
    config: UploadConfig = Depends(get_upload_config)
) -> Dict[str, Any]:
    """
    语音识别接口
    
    上传音频文件并进行语音识别，返回识别结果。
    
    Args:
        file: 上传的音频文件
        config: 上传配置
        
    Returns:
        Dict[str, Any]: 识别结果
        
    Raises:
        HTTPException: 请求处理失败时抛出
    """
    try:
        # 验证文件
        validate_audio_file(file, config)
        
        logger.info(f"接收到音频文件: {file.filename}, 大小: {file.size if hasattr(file, 'size') else '未知'}")
        
        # 读取文件内容
        audio_data = await file.read()
        
        if not audio_data:
            raise HTTPException(status_code=400, detail="文件内容为空")
        
        # 获取文件格式
        file_extension = Path(file.filename).suffix.lower().lstrip('.')
        
        # 创建语音识别服务
        settings = get_settings()
        speech_service = create_speech_recognition_service(settings.aliyun)
        
        # 执行语音识别
        result = speech_service.recognize_bytes(
            audio_data=audio_data,
            audio_format=file_extension,
            sample_rate=16000  # 默认采样率，可以根据需要调整
        )
        
        logger.info(f"语音识别成功: {result.get('text', '')[:50]}...")
        
        return {
            "success": True,
            "message": "语音识别成功",
            "data": {
                "text": result.get('text', ''),
                "begin_time": result.get('begin_time', 0),
                "end_time": result.get('end_time', 0),
                "words": result.get('words', []),
                "request_id": result.get('request_id'),
                "filename": file.filename
            }
        }
        
    except SpeechRecognitionError as e:
        logger.error(f"语音识别失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"语音识别失败: {str(e)}")
    
    except HTTPException:
        # 重新抛出 HTTP 异常
        raise
    
    except Exception as e:
        logger.error(f"处理请求时发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/formats")
async def get_supported_formats() -> Dict[str, Any]:
    """
    获取支持的音频格式
    
    Returns:
        Dict[str, Any]: 支持的音频格式列表
    """
    try:
        settings = get_settings()
        speech_service = create_speech_recognition_service(settings.aliyun)
        
        return {
            "success": True,
            "message": "获取支持格式成功",
            "data": {
                "supported_formats": speech_service.get_supported_formats(),
                "allowed_formats": settings.upload.allowed_formats,
                "max_file_size_mb": settings.upload.max_file_size
            }
        }
        
    except Exception as e:
        logger.error(f"获取支持格式时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    健康检查接口
    
    Returns:
        Dict[str, Any]: 服务状态
    """
    return {
        "success": True,
        "message": "语音识别服务正常",
        "data": {
            "status": "healthy",
            "service": "speech_recognition"
        }
    }
