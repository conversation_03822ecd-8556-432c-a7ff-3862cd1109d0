# 语音识别系统

基于阿里云 Paraformer SDK 的完整语音识别系统，包含 Vue 3 前端和 Python 后端。

## 项目结构

```
paraformer/
├── backend/                 # Python 后端
│   ├── app/                # 应用主目录
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI 应用入口
│   │   ├── config/         # 配置管理
│   │   ├── services/       # 业务服务
│   │   └── api/           # API 路由
│   ├── pyproject.toml      # Python 依赖配置
│   └── config.yaml         # 应用配置文件
├── frontend/               # Vue 3 前端
│   ├── src/
│   │   ├── components/     # Vue 组件
│   │   ├── views/         # 页面视图
│   │   └── utils/         # 工具函数
│   ├── package.json
│   └── vite.config.js
└── README.md
```

## 技术栈

- **后端**: Python + FastAPI + 阿里云 Paraformer SDK
- **前端**: Vue 3 + Composition API + Vite
- **包管理**: uv (Python) + npm (Node.js)

## 功能特性

- 🎤 实时录音功能
- 🔄 音频文件上传
- 🗣️ 语音转文字识别
- 📱 响应式界面设计
- 🔧 模块化架构

## 快速开始

### 系统要求

- **Python**: 3.8或更高版本
- **Node.js**: 16或更高版本
- **uv**: Python包管理器 (`pip install uv`)
- **ffmpeg**: 音频转换工具（可选，用于音频格式转换）

### 项目初始化

```bash
# 1. 进入后端目录
cd backend

# 2. 使用uv同步依赖
uv sync

# 3. 创建配置文件
cp config.yaml.example config.yaml
# 编辑config.yaml，填入阿里云API Key

# 4. 进入前端目录
cd ../frontend

# 5. 安装前端依赖
npm install
```

### 启动服务

#### 方法1: 使用Makefile（最推荐）
```bash
# 安装所有依赖
make install

# 启动开发服务器
make dev

# 启动前端服务器（另开终端）
make frontend

# 启动生产服务器
make prod
```

#### 方法2: uv脚本命令
```bash
cd backend
# 开发模式（热重载）
uv run dev

# 生产模式
uv run start
```

#### 方法3: Python启动脚本
```bash
cd backend
# 开发模式
python start_server.py dev

# 生产模式
python start_server.py prod
```

### 前端启动
```bash
cd frontend
npm run dev
```

## 安装说明

### macOS/Linux用户

1. **安装Python**: 使用系统包管理器或从官网下载Python 3.8+
2. **安装uv**: `pip install uv`
3. **安装Node.js**: 使用包管理器或从官网下载Node.js 16+
4. **安装ffmpeg**（可选）:
   - macOS: `brew install ffmpeg`
   - Ubuntu/Debian: `sudo apt install ffmpeg`
   - CentOS/RHEL: `sudo yum install ffmpeg`

## 配置说明

1. 复制 `backend/config.yaml.example` 为 `backend/config.yaml`
2. 在配置文件中填入阿里云 API Key
3. 使用 `uv sync` 同步后端依赖
4. 使用 `npm install` 安装前端依赖
5. 启动后端和前端服务

## API 文档

启动后端服务后，访问 `http://localhost:8001/docs` 查看 API 文档。

## 故障排除

### 常见问题

1. **SSL证书错误**: 运行 `uv add certifi` 或 `pip install --upgrade certifi`
2. **ffmpeg未找到**: 确保ffmpeg已安装并在PATH中
3. **端口占用**: 修改config.yaml中的端口号（默认8001）
4. **依赖安装失败**: 尝试 `uv sync --reinstall`
5. **API Key错误**: 检查config.yaml中的阿里云API Key是否正确
6. **uv命令不存在**: 运行 `pip install uv` 安装uv包管理器
