<template>
  <div class="audio-recorder">
    <!-- 录音控制区域 -->
    <div class="recorder-controls">
      <button
        :class="[
          'record-button',
          {
            'recording': isRecording,
            'disabled': isProcessing
          }
        ]"
        :disabled="isProcessing"
        @click="toggleRecording"
      >
        <div class="button-content">
          <div class="icon">
            <svg v-if="!isRecording" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
              <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="currentColor">
              <rect x="6" y="6" width="12" height="12" rx="2"/>
            </svg>
          </div>
          <span class="button-text">
            {{ buttonText }}
          </span>
        </div>
        
        <!-- 录音动画效果 -->
        <div v-if="isRecording" class="recording-animation">
          <div class="pulse-ring"></div>
          <div class="pulse-ring delay-1"></div>
          <div class="pulse-ring delay-2"></div>
        </div>
      </button>
      
      <!-- 录音时长显示 -->
      <div v-if="isRecording" class="recording-duration">
        {{ formatDuration(recordingDuration) }}
      </div>
    </div>

    <!-- 状态信息 -->
    <div v-if="statusMessage" class="status-message" :class="statusType">
      {{ statusMessage }}
    </div>

    <!-- 识别结果 -->
    <div v-if="recognitionResult" class="recognition-result">
      <h3>识别结果：</h3>
      <div class="result-text">{{ recognitionResult.text }}</div>
      <div class="result-meta">
        <span>时长: {{ formatDuration(recognitionResult.end_time - recognitionResult.begin_time) }}</span>
        <span>文件: {{ recognitionResult.filename }}</span>
      </div>
    </div>

    <!-- 音频播放 -->
    <div v-if="audioUrl" class="audio-player">
      <audio :src="audioUrl" controls></audio>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { createAudioRecorder, type AudioRecorder, type RecordingResult } from '../utils/audioRecorder';
import { apiService, type SpeechRecognitionResult } from '../utils/api';

// Props
interface Props {
  autoUpload?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoUpload: true
});

// Emits
const emit = defineEmits<{
  recordingStart: [];
  recordingStop: [result: RecordingResult];
  recognitionSuccess: [result: SpeechRecognitionResult];
  recognitionError: [error: string];
}>();

// 响应式数据
const audioRecorder = ref<AudioRecorder | null>(null);
const isRecording = ref(false);
const isProcessing = ref(false);
const recordingDuration = ref(0);
const statusMessage = ref('');
const statusType = ref<'info' | 'success' | 'error' | 'warning'>('info');
const recognitionResult = ref<SpeechRecognitionResult | null>(null);
const audioUrl = ref('');

// 定时器
let durationTimer: number | null = null;

// 计算属性
const buttonText = computed(() => {
  if (isProcessing.value) return '处理中...';
  if (isRecording.value) return '停止录音';
  return '开始录音';
});

// 方法
const showStatus = (message: string, type: typeof statusType.value = 'info') => {
  statusMessage.value = message;
  statusType.value = type;
  
  // 3秒后自动清除状态信息
  setTimeout(() => {
    if (statusMessage.value === message) {
      statusMessage.value = '';
    }
  }, 3000);
};

const formatDuration = (ms: number): string => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const startDurationTimer = () => {
  durationTimer = setInterval(() => {
    if (audioRecorder.value?.isRecording()) {
      recordingDuration.value = audioRecorder.value.getDuration();
    }
  }, 100);
};

const stopDurationTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer);
    durationTimer = null;
  }
  recordingDuration.value = 0;
};

const toggleRecording = async () => {
  if (isProcessing.value) return;

  try {
    if (isRecording.value) {
      await stopRecording();
    } else {
      await startRecording();
    }
  } catch (error) {
    console.error('录音操作失败:', error);
    showStatus(error instanceof Error ? error.message : '录音操作失败', 'error');
  }
};

const startRecording = async () => {
  if (!audioRecorder.value) {
    throw new Error('录音器未初始化');
  }

  // 清除之前的结果
  recognitionResult.value = null;
  audioUrl.value = '';

  showStatus('开始录音...', 'info');
  
  await audioRecorder.value.startRecording();
  isRecording.value = true;
  startDurationTimer();
  
  emit('recordingStart');
  showStatus('录音中，点击按钮停止', 'info');
};

const stopRecording = async () => {
  if (!audioRecorder.value) {
    throw new Error('录音器未初始化');
  }

  isProcessing.value = true;
  stopDurationTimer();
  
  try {
    showStatus('正在停止录音...', 'info');
    
    const result = await audioRecorder.value.stopRecording();
    isRecording.value = false;
    audioUrl.value = result.audioUrl;
    
    emit('recordingStop', result);
    showStatus(`录音完成，时长: ${formatDuration(result.duration)}`, 'success');

    // 自动上传并识别
    if (props.autoUpload) {
      await uploadAndRecognize(result);
    }
  } finally {
    isProcessing.value = false;
  }
};

const uploadAndRecognize = async (recordingResult: RecordingResult) => {
  try {
    showStatus('正在上传并识别...', 'info');

    // 获取音频格式信息
    const blobType = recordingResult.audioBlob.type;
    console.log('录音格式:', blobType);
    console.log('录音大小:', recordingResult.audioBlob.size, 'bytes');
    console.log('录音时长:', recordingResult.duration, 'ms');

    // 确定文件扩展名
    let fileName = 'recording.wav';
    let fileType = 'audio/wav';

    if (blobType.includes('webm')) {
      fileName = 'recording.webm';
      fileType = blobType;
    } else if (blobType.includes('mp4')) {
      fileName = 'recording.mp4';
      fileType = blobType;
    } else if (blobType.includes('ogg')) {
      fileName = 'recording.ogg';
      fileType = blobType;
    }

    // 创建文件对象
    const file = new File([recordingResult.audioBlob], fileName, {
      type: fileType
    });

    console.log('上传文件:', fileName, '类型:', fileType);

    // 调用识别 API
    const result = await apiService.recognizeSpeech(file);

    recognitionResult.value = result;
    emit('recognitionSuccess', result);
    showStatus('语音识别成功！', 'success');

  } catch (error) {
    console.error('语音识别失败:', error);
    const errorMessage = error instanceof Error ? error.message : '语音识别失败';
    emit('recognitionError', errorMessage);
    showStatus(errorMessage, 'error');
  }
};

// 生命周期
onMounted(async () => {
  try {
    // 检查浏览器支持
    if (!createAudioRecorder().constructor.isSupported()) {
      throw new Error('当前浏览器不支持录音功能');
    }

    // 创建录音器
    audioRecorder.value = createAudioRecorder({
      sampleRate: 16000,
      channelCount: 1
    });

    // 请求麦克风权限
    const hasPermission = await audioRecorder.value.requestPermission();
    if (!hasPermission) {
      throw new Error('无法获取麦克风权限');
    }

    showStatus('录音器已就绪', 'success');
    
  } catch (error) {
    console.error('录音器初始化失败:', error);
    showStatus(error instanceof Error ? error.message : '录音器初始化失败', 'error');
  }
});

onUnmounted(() => {
  stopDurationTimer();
  
  if (audioRecorder.value?.isRecording()) {
    audioRecorder.value.cancelRecording();
  }
  
  // 清理音频 URL
  if (audioUrl.value) {
    URL.revokeObjectURL(audioUrl.value);
  }
});
</script>

<style scoped>
.audio-recorder {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.recorder-controls {
  margin-bottom: 2rem;
}

.record-button {
  position: relative;
  width: 120px;
  height: 120px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  margin-bottom: 1rem;
}

.record-button:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.record-button.recording {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.record-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.icon {
  width: 32px;
  height: 32px;
  margin-bottom: 0.5rem;
}

.icon svg {
  width: 100%;
  height: 100%;
}

.button-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.recording-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.pulse-ring.delay-1 {
  animation-delay: 0.5s;
}

.pulse-ring.delay-2 {
  animation-delay: 1s;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

.recording-duration {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ff6b6b;
  font-family: 'Courier New', monospace;
}

.status-message {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.status-message.info {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.status-message.success {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.status-message.error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

.status-message.warning {
  background-color: #fff3e0;
  color: #ef6c00;
  border: 1px solid #ffcc02;
}

.recognition-result {
  background: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  text-align: left;
}

.recognition-result h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.125rem;
}

.result-text {
  background: white;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #e9ecef;
  font-size: 1rem;
  line-height: 1.5;
  color: #333;
  margin-bottom: 1rem;
  min-height: 3rem;
}

.result-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.audio-player {
  margin-top: 1rem;
}

.audio-player audio {
  width: 100%;
  max-width: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .audio-recorder {
    padding: 1rem;
  }

  .record-button {
    width: 100px;
    height: 100px;
  }

  .pulse-ring {
    width: 100px;
    height: 100px;
  }

  .result-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
