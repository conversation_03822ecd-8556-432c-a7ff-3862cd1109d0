[project]
name = "speech-recognition-backend"
version = "1.0.0"
description = "基于阿里云 Paraformer SDK 的语音识别后端服务"
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.20.0",
    "python-multipart>=0.0.6",
    "pyyaml>=6.0",
    "requests>=2.28.0",
    "dashscope>=1.20.0",
    "pydantic>=2.0.0",
    "certifi>=2023.0.0",
]

[project.scripts]
start = "app.main:start_server"
dev = "app.main:start_dev_server"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
]
