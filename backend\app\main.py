"""
语音识别系统后端主应用

基于 FastAPI 的语音识别服务，提供文件上传和语音转文字功能。
"""

import logging
import os
import ssl
import platform
from pathlib import Path

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .config.settings import get_settings
from .api.speech import router as speech_router


def setup_ssl_certificates():
    """设置SSL证书"""
    logger = logging.getLogger(__name__)

    try:
        import certifi
        cert_path = certifi.where()

        # 设置环境变量
        os.environ['SSL_CERT_FILE'] = cert_path
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        os.environ['CURL_CA_BUNDLE'] = cert_path

        logger.info(f"SSL证书路径已设置: {cert_path}")
        logger.info(f"当前操作系统: {platform.system()}")

        # 验证证书文件
        if os.path.exists(cert_path):
            logger.info("SSL证书文件验证成功")
        else:
            logger.warning(f"SSL证书文件不存在: {cert_path}")

    except ImportError:
        logger.error("certifi包未安装，无法设置SSL证书")
        logger.info("请运行: uv add certifi")
    except Exception as e:
        logger.error(f"SSL证书设置失败: {e}")

        # 提供平台特定的解决建议
        if platform.system() == 'Darwin':  # macOS
            logger.info("macOS用户可以尝试运行: /Applications/Python\\ 3.x/Install\\ Certificates.command")
        else:  # Linux
            logger.info("Linux用户请确保ca-certificates包已安装")


def setup_logging():
    """设置日志配置"""
    try:
        settings = get_settings()
        logging_config = settings.logging
        
        logging.basicConfig(
            level=getattr(logging, logging_config.level.upper()),
            format=logging_config.format,
            handlers=[
                logging.StreamHandler(),
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info("日志系统初始化完成")
        
    except Exception as e:
        # 如果配置加载失败，使用默认日志配置
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        logger = logging.getLogger(__name__)
        logger.warning(f"使用默认日志配置，配置加载失败: {e}")


def create_temp_directory():
    """创建临时文件目录"""
    try:
        settings = get_settings()
        temp_dir = Path(settings.upload.temp_dir)
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        logger = logging.getLogger(__name__)
        logger.info(f"临时目录已创建: {temp_dir}")
        
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.warning(f"创建临时目录失败: {e}")


def create_app() -> FastAPI:
    """
    创建 FastAPI 应用实例

    Returns:
        FastAPI: 应用实例
    """
    # 设置SSL证书
    setup_ssl_certificates()

    # 设置日志
    setup_logging()

    # 创建临时目录
    create_temp_directory()
    
    # 创建 FastAPI 应用
    app = FastAPI(
        title="语音识别系统",
        description="基于阿里云 Paraformer SDK 的语音识别服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 配置 CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该限制具体的域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(speech_router)
    
    # 全局异常处理
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger = logging.getLogger(__name__)
        logger.error(f"全局异常处理: {str(exc)}")
        
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": "服务器内部错误",
                "detail": str(exc) if app.debug else "请联系管理员"
            }
        )
    
    # 根路径
    @app.get("/")
    async def root():
        """根路径，返回服务信息"""
        return {
            "success": True,
            "message": "语音识别系统后端服务",
            "data": {
                "service": "speech_recognition_backend",
                "version": "1.0.0",
                "docs": "/docs",
                "health": "/api/speech/health"
            }
        }
    
    # 健康检查
    @app.get("/health")
    async def health():
        """健康检查"""
        return {
            "success": True,
            "message": "服务正常",
            "data": {
                "status": "healthy"
            }
        }
    
    logger = logging.getLogger(__name__)
    logger.info("FastAPI 应用创建完成")
    
    return app


# 创建应用实例
app = create_app()


def start_server():
    """生产环境启动函数 - 用于 uv run start"""
    import uvicorn

    try:
        settings = get_settings()
        server_config = settings.server

        uvicorn.run(
            "app.main:app",
            host=server_config.host,
            port=server_config.port,
            reload=False,  # 生产环境不使用热重载
            log_level="info"
        )
    except Exception as e:
        print(f"启动服务失败: {e}")
        # 使用默认配置启动
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8001,
            reload=False,
            log_level="info"
        )


def start_dev_server():
    """开发环境启动函数 - 用于 uv run dev"""
    import uvicorn

    try:
        settings = get_settings()
        server_config = settings.server

        uvicorn.run(
            "app.main:app",
            host=server_config.host,
            port=server_config.port,
            reload=True,  # 开发环境使用热重载
            log_level="debug"
        )
    except Exception as e:
        print(f"启动开发服务失败: {e}")
        # 使用默认配置启动
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8001,
            reload=True,
            log_level="debug"
        )


if __name__ == "__main__":
    # 直接运行时使用开发模式
    start_dev_server()
