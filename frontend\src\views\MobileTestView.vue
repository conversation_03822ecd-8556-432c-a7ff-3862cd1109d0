<template>
  <div class="mobile-test-view">
    <div class="test-header">
      <h1>移动端测试</h1>
      <p>测试不同屏幕尺寸下的语音助手界面</p>
    </div>
    
    <div class="test-sections">
      <!-- 屏幕信息 -->
      <section class="test-section">
        <h2>屏幕信息</h2>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">屏幕宽度:</span>
            <span class="value">{{ screenInfo.width }}px</span>
          </div>
          <div class="info-item">
            <span class="label">屏幕高度:</span>
            <span class="value">{{ screenInfo.height }}px</span>
          </div>
          <div class="info-item">
            <span class="label">设备像素比:</span>
            <span class="value">{{ screenInfo.devicePixelRatio }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户代理:</span>
            <span class="value">{{ deviceInfo.userAgent }}</span>
          </div>
          <div class="info-item">
            <span class="label">触摸支持:</span>
            <span class="value">{{ deviceInfo.touchSupport ? '是' : '否' }}</span>
          </div>
          <div class="info-item">
            <span class="label">方向:</span>
            <span class="value">{{ screenInfo.orientation }}</span>
          </div>
        </div>
      </section>
      
      <!-- 断点测试 -->
      <section class="test-section">
        <h2>响应式断点</h2>
        <div class="breakpoint-indicators">
          <div class="breakpoint" :class="{ active: screenInfo.width <= 480 }">
            超小屏 (≤480px)
          </div>
          <div class="breakpoint" :class="{ active: screenInfo.width > 480 && screenInfo.width <= 768 }">
            小屏 (481-768px)
          </div>
          <div class="breakpoint" :class="{ active: screenInfo.width > 768 && screenInfo.width <= 1024 }">
            中屏 (769-1024px)
          </div>
          <div class="breakpoint" :class="{ active: screenInfo.width > 1024 }">
            大屏 (>1024px)
          </div>
        </div>
      </section>
      
      <!-- 语音助手组件测试 -->
      <section class="test-section">
        <h2>语音助手组件</h2>
        <div class="voice-assistant-container">
          <VoiceAssistant
            :auto-upload="false"
            @recording-start="onRecordingStart"
            @recording-stop="onRecordingStop"
            @recognition-success="onRecognitionSuccess"
            @recognition-error="onRecognitionError"
          />
        </div>
      </section>
      
      <!-- 测试结果 -->
      <section class="test-section" v-if="testResults.length > 0">
        <h2>测试结果</h2>
        <div class="test-results">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="test-result"
            :class="result.type"
          >
            <div class="result-time">{{ result.timestamp.toLocaleTimeString() }}</div>
            <div class="result-message">{{ result.message }}</div>
          </div>
        </div>
      </section>
    </div>
    
    <!-- 返回按钮 -->
    <div class="back-button">
      <router-link to="/" class="btn-back">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
        返回主页
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import VoiceAssistant from '@/components/VoiceAssistant.vue';
import type { RecordingResult } from '@/utils/audioRecorder';
import type { SpeechRecognitionResult } from '@/utils/api';

// 响应式数据
const screenInfo = ref({
  width: window.innerWidth,
  height: window.innerHeight,
  devicePixelRatio: window.devicePixelRatio,
  orientation: window.innerWidth > window.innerHeight ? '横屏' : '竖屏'
});

const deviceInfo = ref({
  userAgent: navigator.userAgent.includes('Mobile') ? '移动设备' : '桌面设备',
  touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0
});

const testResults = ref<Array<{
  type: 'info' | 'success' | 'error';
  message: string;
  timestamp: Date;
}>>([]);

// 方法
const updateScreenInfo = () => {
  screenInfo.value = {
    width: window.innerWidth,
    height: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio,
    orientation: window.innerWidth > window.innerHeight ? '横屏' : '竖屏'
  };
};

const addTestResult = (type: 'info' | 'success' | 'error', message: string) => {
  testResults.value.unshift({
    type,
    message,
    timestamp: new Date()
  });
  
  // 限制结果数量
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10);
  }
};

// 事件处理
const onRecordingStart = () => {
  addTestResult('info', '开始录音测试');
};

const onRecordingStop = (result: RecordingResult) => {
  addTestResult('success', `录音结束，时长: ${(result.duration / 1000).toFixed(1)}秒`);
};

const onRecognitionSuccess = (result: SpeechRecognitionResult) => {
  addTestResult('success', `识别成功: ${result.text}`);
};

const onRecognitionError = (error: string) => {
  addTestResult('error', `识别失败: ${error}`);
};

// 生命周期
onMounted(() => {
  window.addEventListener('resize', updateScreenInfo);
  window.addEventListener('orientationchange', updateScreenInfo);
  
  addTestResult('info', '移动端测试页面已加载');
});

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenInfo);
  window.removeEventListener('orientationchange', updateScreenInfo);
});
</script>

<style scoped>
.mobile-test-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
}

.test-header {
  text-align: center;
  margin-bottom: 2rem;
}

.test-header h1 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.test-header p {
  opacity: 0.8;
}

.test-sections {
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.test-section h2 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.info-grid {
  display: grid;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
}

.label {
  font-weight: 600;
}

.value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.875rem;
  opacity: 0.9;
}

.breakpoint-indicators {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.breakpoint {
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.breakpoint.active {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.4);
  font-weight: 600;
}

.voice-assistant-container {
  display: flex;
  justify-content: center;
  padding: 2rem 0;
}

.test-results {
  max-height: 300px;
  overflow-y: auto;
}

.test-result {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  border-left: 4px solid;
}

.test-result.info {
  background: rgba(116, 192, 252, 0.2);
  border-left-color: #74c0fc;
}

.test-result.success {
  background: rgba(81, 207, 102, 0.2);
  border-left-color: #51cf66;
}

.test-result.error {
  background: rgba(255, 107, 107, 0.2);
  border-left-color: #ff6b6b;
}

.result-time {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-bottom: 0.25rem;
}

.result-message {
  font-size: 0.875rem;
}

.back-button {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  z-index: 1000;
}

.btn-back {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 2rem;
  color: white;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-back:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.btn-back svg {
  width: 16px;
  height: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-test-view {
    padding: 0.75rem;
  }
  
  .test-header h1 {
    font-size: 1.5rem;
  }
  
  .test-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .info-item {
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
  }
  
  .breakpoint-indicators {
    grid-template-columns: 1fr 1fr;
  }
  
  .back-button {
    bottom: 1rem;
    left: 1rem;
  }
}

@media (max-width: 480px) {
  .breakpoint-indicators {
    grid-template-columns: 1fr;
  }
  
  .value {
    font-size: 0.75rem;
    word-break: break-all;
  }
}
</style>
