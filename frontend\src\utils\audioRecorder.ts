/**
 * 音频录制工具类
 * 
 * 提供浏览器音频录制功能，支持录音控制和音频数据获取。
 */

export interface AudioRecorderOptions {
  /** 采样率 */
  sampleRate?: number;
  /** 音频通道数 */
  channelCount?: number;
  /** 音频格式 */
  mimeType?: string;
}

export interface RecordingResult {
  /** 音频 Blob 数据 */
  audioBlob: Blob;
  /** 录音时长（毫秒） */
  duration: number;
  /** 音频 URL（用于播放） */
  audioUrl: string;
}

export class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private stream: MediaStream | null = null;
  private startTime: number = 0;
  private options: AudioRecorderOptions;

  constructor(options: AudioRecorderOptions = {}) {
    this.options = {
      sampleRate: 16000,
      channelCount: 1,
      mimeType: 'audio/wav',
      ...options
    };
  }

  /**
   * 检查浏览器是否支持录音
   */
  static isSupported(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia && window.MediaRecorder);
  }

  /**
   * 请求麦克风权限
   */
  async requestPermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('麦克风权限请求失败:', error);
      return false;
    }
  }

  /**
   * 开始录音
   */
  async startRecording(): Promise<void> {
    if (!AudioRecorder.isSupported()) {
      throw new Error('当前浏览器不支持录音功能');
    }

    try {
      // 获取音频流
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: { ideal: this.options.sampleRate },
          channelCount: { ideal: this.options.channelCount },
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          // 添加更多音频质量设置
          latency: 0.01,
          volume: 1.0
        }
      });

      // 清空之前的录音数据
      this.audioChunks = [];

      // 创建 MediaRecorder
      const mimeType = this.getSupportedMimeType();
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType
      });

      // 设置事件监听器
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onerror = (event) => {
        console.error('录音错误:', event);
      };

      // 开始录音
      this.startTime = Date.now();
      this.mediaRecorder.start(100); // 每100ms收集一次数据

    } catch (error) {
      console.error('开始录音失败:', error);
      throw new Error('无法访问麦克风，请检查权限设置');
    }
  }

  /**
   * 停止录音
   */
  async stopRecording(): Promise<RecordingResult> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
        reject(new Error('录音器未启动'));
        return;
      }

      this.mediaRecorder.onstop = () => {
        try {
          const duration = Date.now() - this.startTime;
          const mimeType = this.getSupportedMimeType();
          const audioBlob = new Blob(this.audioChunks, { type: mimeType });
          const audioUrl = URL.createObjectURL(audioBlob);

          // 停止音频流
          if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
          }

          resolve({
            audioBlob,
            duration,
            audioUrl
          });
        } catch (error) {
          reject(error);
        }
      };

      this.mediaRecorder.stop();
    });
  }

  /**
   * 取消录音
   */
  cancelRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }

    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }

    this.audioChunks = [];
  }

  /**
   * 获取录音状态
   */
  getState(): string {
    return this.mediaRecorder?.state || 'inactive';
  }

  /**
   * 是否正在录音
   */
  isRecording(): boolean {
    return this.mediaRecorder?.state === 'recording';
  }

  /**
   * 获取支持的 MIME 类型
   */
  private getSupportedMimeType(): string {
    const types = [
      'audio/wav',
      'audio/webm;codecs=opus',
      'audio/webm;codecs=pcm',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus'
    ];

    console.log('检查支持的音频格式:');
    for (const type of types) {
      const supported = MediaRecorder.isTypeSupported(type);
      console.log(`${type}: ${supported}`);
      if (supported) {
        console.log(`选择音频格式: ${type}`);
        return type;
      }
    }

    console.log('使用默认格式: audio/webm');
    return 'audio/webm'; // 默认类型
  }

  /**
   * 获取录音时长（毫秒）
   */
  getDuration(): number {
    if (this.startTime === 0) return 0;
    return Date.now() - this.startTime;
  }
}

/**
 * 创建音频录制器实例
 */
export function createAudioRecorder(options?: AudioRecorderOptions): AudioRecorder {
  return new AudioRecorder(options);
}
