# 语音识别系统 Makefile
# 使用 uv 包管理器

.PHONY: help install dev prod frontend clean test

# 默认目标
help:
	@echo "语音识别系统 - 可用命令:"
	@echo ""
	@echo "  make install    - 安装所有依赖"
	@echo "  make dev        - 启动开发服务器"
	@echo "  make prod       - 启动生产服务器"
	@echo "  make frontend   - 启动前端开发服务器"
	@echo "  make clean      - 清理临时文件"
	@echo "  make test       - 运行测试"
	@echo ""

# 安装依赖
install:
	@echo "📦 安装后端依赖..."
	cd backend && uv sync
	@echo "📦 安装前端依赖..."
	cd frontend && npm install
	@echo "✅ 依赖安装完成"

# 开发模式启动后端
dev:
	@echo "🚀 启动开发服务器..."
	cd backend && uv run dev

# 生产模式启动后端
prod:
	@echo "🚀 启动生产服务器..."
	cd backend && uv run start

# 启动前端
frontend:
	@echo "🚀 启动前端开发服务器..."
	cd frontend && npm run dev

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -name "*.pyc" -delete 2>/dev/null || true
	rm -rf backend/temp/* 2>/dev/null || true
	@echo "✅ 清理完成"

# 运行测试
test:
	@echo "🧪 运行后端测试..."
	cd backend && uv run pytest
	@echo "✅ 测试完成"

# 检查配置
check-config:
	@echo "🔍 检查配置文件..."
	@if [ ! -f backend/config.yaml ]; then \
		echo "❌ 配置文件不存在，正在创建..."; \
		cd backend && cp config.yaml.example config.yaml; \
		echo "⚠️  请编辑 backend/config.yaml 文件，填入阿里云API Key"; \
	else \
		echo "✅ 配置文件存在"; \
	fi

# 完整启动（后端+前端）
start-all: check-config
	@echo "🚀 启动完整服务..."
	@echo "启动后端服务..."
	cd backend && uv run dev &
	@sleep 3
	@echo "启动前端服务..."
	cd frontend && npm run dev

# 同步依赖
sync:
	@echo "🔄 同步后端依赖..."
	cd backend && uv sync
	@echo "✅ 依赖同步完成"
