"""
音频转换服务模块

提供音频格式转换功能，将不支持的格式转换为WAV格式。
"""

import os
import tempfile
import logging
import subprocess
import platform
import shutil
from typing import Optional, Tuple
from pathlib import Path


class AudioConversionError(Exception):
    """音频转换异常"""
    pass


class AudioConverter:
    """
    音频转换器
    
    支持将各种音频格式转换为WAV格式，以便语音识别使用。
    """
    
    def __init__(self):
        """初始化音频转换器"""
        self.logger = logging.getLogger(__name__)

        # 支持的输入格式
        self.supported_input_formats = [
            'mp3', 'wav', 'flac', 'm4a', 'aac', 'ogg', 'webm', 'opus'
        ]

        # 目标格式配置
        self.target_format = 'wav'
        self.target_sample_rate = 16000
        self.target_channels = 1  # 单声道

        # 检查ffmpeg可用性
        self.ffmpeg_available = self._check_ffmpeg_availability()

    def _check_ffmpeg_availability(self) -> bool:
        """
        检查ffmpeg是否可用

        Returns:
            bool: ffmpeg是否可用
        """
        try:
            # 尝试运行ffmpeg -version
            ffmpeg_cmd = 'ffmpeg.exe' if platform.system() == 'Windows' else 'ffmpeg'

            # 首先检查是否在PATH中
            if shutil.which(ffmpeg_cmd) is None:
                self.logger.warning(f"{ffmpeg_cmd} 不在PATH中")
                return False

            # 尝试执行ffmpeg版本命令
            result = subprocess.run(
                [ffmpeg_cmd, '-version'],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                self.logger.info(f"ffmpeg可用: {ffmpeg_cmd}")
                return True
            else:
                self.logger.warning(f"ffmpeg执行失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            self.logger.warning("ffmpeg版本检查超时")
            return False
        except FileNotFoundError:
            self.logger.warning("ffmpeg未找到")
            return False
        except Exception as e:
            self.logger.warning(f"ffmpeg检查失败: {e}")
            return False

    def is_supported_format(self, format_name: str) -> bool:
        """
        检查是否支持该格式
        
        Args:
            format_name: 音频格式名称
            
        Returns:
            bool: 是否支持
        """
        return format_name.lower() in self.supported_input_formats
    
    def convert_to_wav(
        self,
        input_file_path: str,
        output_file_path: Optional[str] = None
    ) -> str:
        """
        将音频文件转换为WAV格式（使用ffmpeg）

        Args:
            input_file_path: 输入文件路径
            output_file_path: 输出文件路径，如果为None则创建临时文件

        Returns:
            str: 输出文件路径

        Raises:
            AudioConversionError: 转换失败时抛出
        """
        try:
            # 检查ffmpeg是否可用
            if not self.ffmpeg_available:
                raise AudioConversionError("ffmpeg不可用，无法进行音频转换。请安装ffmpeg并确保其在PATH中。")

            # 检查输入文件是否存在
            if not os.path.exists(input_file_path):
                raise AudioConversionError(f"输入文件不存在: {input_file_path}")

            # 获取输入文件信息
            input_path = Path(input_file_path)
            input_format = input_path.suffix.lower().lstrip('.')

            self.logger.info(f"开始转换音频文件: {input_file_path}")
            self.logger.info(f"输入格式: {input_format}")

            # 确定输出文件路径
            if output_file_path is None:
                # 创建临时文件
                temp_fd, output_file_path = tempfile.mkstemp(suffix='.wav')
                os.close(temp_fd)  # 关闭文件描述符，只保留路径

            # 确定ffmpeg可执行文件名
            ffmpeg_exe = 'ffmpeg.exe' if platform.system() == 'Windows' else 'ffmpeg'

            # 使用ffmpeg进行转换
            ffmpeg_cmd = [
                ffmpeg_exe,
                '-i', str(input_path),  # 输入文件（使用字符串路径）
                '-ar', str(self.target_sample_rate),  # 采样率
                '-ac', str(self.target_channels),  # 声道数
                '-f', 'wav',  # 输出格式
                '-y',  # 覆盖输出文件
                str(output_file_path)  # 输出文件（使用字符串路径）
            ]

            self.logger.info(f"执行ffmpeg命令: {' '.join(ffmpeg_cmd)}")

            # 执行ffmpeg命令
            try:
                result = subprocess.run(
                    ffmpeg_cmd,
                    capture_output=True,
                    text=True,
                    timeout=30  # 30秒超时
                )

                if result.returncode != 0:
                    error_msg = f"ffmpeg转换失败: {result.stderr}"
                    self.logger.error(error_msg)
                    raise AudioConversionError(error_msg)

                self.logger.info(f"ffmpeg转换成功: {result.stdout}")

            except subprocess.TimeoutExpired:
                raise AudioConversionError("音频转换超时")
            except FileNotFoundError:
                raise AudioConversionError("ffmpeg未安装或不在PATH中")
            except Exception as e:
                raise AudioConversionError(f"执行ffmpeg失败: {e}")

            # 验证输出文件
            if not os.path.exists(output_file_path):
                raise AudioConversionError("转换后的文件未生成")

            output_size = os.path.getsize(output_file_path)
            self.logger.info(f"转换完成: {output_file_path}, 大小: {output_size} bytes")

            return output_file_path

        except AudioConversionError:
            # 重新抛出音频转换异常
            raise
        except Exception as e:
            error_msg = f"音频转换过程中发生未知错误: {str(e)}"
            self.logger.error(error_msg)
            raise AudioConversionError(error_msg)
    
    def convert_bytes_to_wav(
        self, 
        audio_data: bytes, 
        input_format: str,
        output_file_path: Optional[str] = None
    ) -> str:
        """
        将音频字节数据转换为WAV文件
        
        Args:
            audio_data: 音频字节数据
            input_format: 输入格式
            output_file_path: 输出文件路径，如果为None则创建临时文件
            
        Returns:
            str: 输出文件路径
            
        Raises:
            AudioConversionError: 转换失败时抛出
        """
        temp_input_file = None
        try:
            # 创建临时输入文件
            suffix = f'.{input_format}'
            temp_fd, temp_input_file = tempfile.mkstemp(suffix=suffix)
            
            with os.fdopen(temp_fd, 'wb') as f:
                f.write(audio_data)
            
            # 转换文件
            result = self.convert_to_wav(temp_input_file, output_file_path)
            
            return result
            
        finally:
            # 清理临时输入文件
            if temp_input_file and os.path.exists(temp_input_file):
                try:
                    os.unlink(temp_input_file)
                except Exception as e:
                    self.logger.warning(f"删除临时文件失败: {e}")
    
    def get_audio_info(self, file_path: str) -> dict:
        """
        获取音频文件信息（使用ffprobe）

        Args:
            file_path: 音频文件路径

        Returns:
            dict: 音频信息
        """
        try:
            # 使用ffprobe获取音频信息
            ffprobe_cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                file_path
            ]

            result = subprocess.run(
                ffprobe_cmd,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                self.logger.error(f"ffprobe失败: {result.stderr}")
                return {}

            import json
            info = json.loads(result.stdout)

            # 提取音频流信息
            audio_stream = None
            for stream in info.get('streams', []):
                if stream.get('codec_type') == 'audio':
                    audio_stream = stream
                    break

            if not audio_stream:
                return {}

            return {
                'duration_ms': float(audio_stream.get('duration', 0)) * 1000,
                'frame_rate': int(audio_stream.get('sample_rate', 0)),
                'channels': int(audio_stream.get('channels', 0)),
                'format': Path(file_path).suffix.lower().lstrip('.'),
                'codec': audio_stream.get('codec_name', ''),
                'bit_rate': int(audio_stream.get('bit_rate', 0))
            }

        except Exception as e:
            self.logger.error(f"获取音频信息失败: {e}")
            return {}


def create_audio_converter() -> AudioConverter:
    """
    创建音频转换器实例的工厂函数
    
    Returns:
        AudioConverter: 音频转换器实例
    """
    return AudioConverter()
