#!/usr/bin/env node
/**
 * 前端功能测试脚本
 *
 * 测试前端与后端API的连接和基本功能
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置
const FRONTEND_URL = 'http://localhost:5174';
const BACKEND_URL = 'http://localhost:8001';

// 测试结果
let testResults = {
  passed: 0,
  failed: 0,
  total: 0
};

// 工具函数
function logTest(name, success, message = '') {
  testResults.total++;
  if (success) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}: ${message}`);
  }
}

function logInfo(message) {
  console.log(`ℹ️  ${message}`);
}

function logSection(title) {
  console.log(`\n🔍 ${title}`);
  console.log('='.repeat(50));
}

// 测试函数
async function testFrontendServer() {
  logSection('测试前端服务器');
  
  try {
    const response = await axios.get(FRONTEND_URL, { timeout: 5000 });
    logTest('前端服务器可访问', response.status === 200);
    
    // 检查是否包含Vue应用
    const hasVueApp = response.data.includes('id="app"');
    logTest('Vue应用容器存在', hasVueApp);
    
  } catch (error) {
    logTest('前端服务器可访问', false, error.message);
  }
}

async function testBackendConnection() {
  logSection('测试后端连接');
  
  try {
    // 测试健康检查
    const healthResponse = await axios.get(`${BACKEND_URL}/health`, { timeout: 5000 });
    logTest('后端健康检查', healthResponse.status === 200 && healthResponse.data.success);
    
    // 测试语音服务健康检查
    const speechHealthResponse = await axios.get(`${BACKEND_URL}/api/speech/health`, { timeout: 5000 });
    logTest('语音服务健康检查', speechHealthResponse.status === 200 && speechHealthResponse.data.success);
    
    // 测试支持格式API
    const formatsResponse = await axios.get(`${BACKEND_URL}/api/speech/formats`, { timeout: 5000 });
    logTest('支持格式API', formatsResponse.status === 200 && formatsResponse.data.success);
    
    if (formatsResponse.data.success) {
      const formats = formatsResponse.data.data;
      logInfo(`支持的格式: ${formats.supported_formats?.join(', ') || '未知'}`);
      logInfo(`允许的格式: ${formats.allowed_formats?.join(', ') || '未知'}`);
      logInfo(`最大文件大小: ${formats.max_file_size_mb || '未知'}MB`);
    }
    
  } catch (error) {
    logTest('后端连接', false, error.message);
  }
}

async function testCORS() {
  logSection('测试CORS配置');
  
  try {
    // 模拟前端请求
    const response = await axios.get(`${BACKEND_URL}/api/speech/health`, {
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      },
      timeout: 5000
    });
    
    logTest('CORS请求成功', response.status === 200);
    
  } catch (error) {
    if (error.response && error.response.status === 200) {
      logTest('CORS请求成功', true);
    } else {
      logTest('CORS配置', false, error.message);
    }
  }
}

async function testAPIEndpoints() {
  logSection('测试API端点');
  
  const endpoints = [
    { path: '/', name: '根端点' },
    { path: '/health', name: '基础健康检查' },
    { path: '/api/speech/health', name: '语音服务健康检查' },
    { path: '/api/speech/formats', name: '支持格式查询' },
    { path: '/docs', name: 'API文档' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(`${BACKEND_URL}${endpoint.path}`, { timeout: 5000 });
      logTest(endpoint.name, response.status === 200);
    } catch (error) {
      logTest(endpoint.name, false, error.response?.status || error.message);
    }
  }
}

async function testBrowserFeatures() {
  logSection('测试浏览器功能支持');
  
  // 这些测试需要在浏览器环境中运行，这里只是检查基本的Node.js环境
  logInfo('以下功能需要在浏览器中测试:');
  logInfo('- MediaRecorder API 支持');
  logInfo('- getUserMedia API 支持');
  logInfo('- Web Audio API 支持');
  logInfo('- File API 支持');
  logInfo('- Blob API 支持');
  
  logTest('Node.js环境检查', true, '请在浏览器中手动测试录音功能');
}

async function testConfiguration() {
  logSection('测试配置一致性');
  
  try {
    // 检查前端API配置是否正确
    
    const apiFilePath = path.join(__dirname, 'src', 'utils', 'api.ts');
    if (fs.existsSync(apiFilePath)) {
      const apiContent = fs.readFileSync(apiFilePath, 'utf8');
      const hasCorrectPort = apiContent.includes('localhost:8001');
      logTest('前端API端口配置', hasCorrectPort);
    } else {
      logTest('API配置文件存在', false, 'api.ts文件不存在');
    }
    
    // 检查Vite配置
    const viteConfigPath = path.join(__dirname, 'vite.config.ts');
    if (fs.existsSync(viteConfigPath)) {
      const viteContent = fs.readFileSync(viteConfigPath, 'utf8');
      const hasProxy = viteContent.includes('proxy') && viteContent.includes('8001');
      logTest('Vite代理配置', hasProxy);
    } else {
      logTest('Vite配置文件存在', false, 'vite.config.ts文件不存在');
    }
    
  } catch (error) {
    logTest('配置检查', false, error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始前端功能测试...');
  console.log('=' * 50);
  
  await testFrontendServer();
  await testBackendConnection();
  await testCORS();
  await testAPIEndpoints();
  await testBrowserFeatures();
  await testConfiguration();
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总');
  console.log('=' * 50);
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed}`);
  console.log(`失败: ${testResults.failed}`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有测试通过！前端配置正确。');
    process.exit(0);
  } else {
    console.log('\n⚠️  部分测试失败，请检查配置。');
    process.exit(1);
  }
}

// 运行测试
runTests().catch(error => {
  console.error('测试执行失败:', error);
  process.exit(1);
});
