<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import VoiceAssistant from '@/components/VoiceAssistant.vue';
import { apiService, type SpeechRecognitionResult } from '@/utils/api';
import type { RecordingResult } from '@/utils/audioRecorder';

// 响应式数据
const isServiceHealthy = ref(false);
const conversationHistory = ref<Array<{
  id: string;
  type: 'user' | 'assistant';
  text: string;
  timestamp: Date;
  duration?: number;
}>>([]);

// 计算属性
const currentTime = ref(new Date());
const greeting = computed(() => {
  const hour = currentTime.value.getHours();
  if (hour < 6) return '夜深了';
  if (hour < 12) return '早上好';
  if (hour < 18) return '下午好';
  return '晚上好';
});

// 更新时间
setInterval(() => {
  currentTime.value = new Date();
}, 1000);

// 事件处理
const onRecordingStart = () => {
  console.log('开始录音...');
};

const onRecordingStop = (result: RecordingResult) => {
  console.log('录音结束:', result);
};

const onRecognitionSuccess = (result: SpeechRecognitionResult) => {
  console.log('识别成功:', result);

  // 添加用户消息到对话历史
  const userMessage = {
    id: `user-${Date.now()}`,
    type: 'user' as const,
    text: result.text,
    timestamp: new Date(),
    duration: result.end_time - result.begin_time
  };

  conversationHistory.value.push(userMessage);

  // 模拟助手回复（这里可以集成真正的AI助手）
  setTimeout(() => {
    const assistantMessage = {
      id: `assistant-${Date.now()}`,
      type: 'assistant' as const,
      text: generateAssistantResponse(result.text),
      timestamp: new Date()
    };
    conversationHistory.value.push(assistantMessage);
  }, 1000);
};

const onRecognitionError = (error: string) => {
  console.error('识别失败:', error);

  // 添加错误消息
  const errorMessage = {
    id: `error-${Date.now()}`,
    type: 'assistant' as const,
    text: '抱歉，我没有听清楚，请再说一遍。',
    timestamp: new Date()
  };
  conversationHistory.value.push(errorMessage);
};

// 生成助手回复（简单示例）
const generateAssistantResponse = (userText: string): string => {
  const responses = [
    `我听到您说："${userText}"。这是一个语音识别测试。`,
    `您刚才说的是："${userText}"，语音识别功能正常工作。`,
    `收到您的消息："${userText}"。有什么我可以帮助您的吗？`,
    `我理解您说的是："${userText}"。语音识别系统运行正常。`
  ];

  return responses[Math.floor(Math.random() * responses.length)];
};

// 清空对话历史
const clearHistory = () => {
  conversationHistory.value = [];
};

// 生命周期
onMounted(async () => {
  try {
    // 检查服务健康状态
    isServiceHealthy.value = await apiService.healthCheck();

    if (isServiceHealthy.value) {
      // 添加欢迎消息
      const welcomeMessage = {
        id: `welcome-${Date.now()}`,
        type: 'assistant' as const,
        text: `${greeting.value}！我是您的语音助手，请点击麦克风按钮开始对话。`,
        timestamp: new Date()
      };
      conversationHistory.value.push(welcomeMessage);
    } else {
      // 服务不可用消息
      const errorMessage = {
        id: `error-${Date.now()}`,
        type: 'assistant' as const,
        text: '抱歉，语音识别服务暂时不可用，请稍后再试。',
        timestamp: new Date()
      };
      conversationHistory.value.push(errorMessage);
    }
  } catch (error) {
    console.error('初始化失败:', error);
  }
});
</script>

<template>
  <div class="voice-assistant-app">
    <!-- 顶部状态栏 -->
    <header class="app-header">
      <div class="status-bar">
        <div class="time">{{ currentTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) }}</div>
        <div class="service-status" :class="{ online: isServiceHealthy, offline: !isServiceHealthy }">
          <div class="status-dot"></div>
          <span>{{ isServiceHealthy ? '在线' : '离线' }}</span>
        </div>
      </div>

      <div class="greeting">
        <h1>{{ greeting }}</h1>
        <p>我是您的语音助手</p>
      </div>
    </header>

    <!-- 对话区域 -->
    <main class="conversation-area">
      <div class="messages-container" ref="messagesContainer">
        <div
          v-for="message in conversationHistory"
          :key="message.id"
          class="message"
          :class="{ 'user-message': message.type === 'user', 'assistant-message': message.type === 'assistant' }"
        >
          <div class="message-avatar">
            <div v-if="message.type === 'user'" class="user-avatar">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
            </div>
            <div v-else class="assistant-avatar">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 1.5c-5.79 0-10.5 4.71-10.5 10.5s4.71 10.5 10.5 10.5 10.5-4.71 10.5-10.5-4.71-10.5-10.5-10.5zm0 19c-4.69 0-8.5-3.81-8.5-8.5s3.81-8.5 8.5-8.5 8.5 3.81 8.5 8.5-3.81 8.5-8.5 8.5zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
              </svg>
            </div>
          </div>

          <div class="message-content">
            <div class="message-bubble">
              <p>{{ message.text }}</p>
              <div class="message-meta">
                <span class="timestamp">{{ message.timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) }}</span>
                <span v-if="message.duration" class="duration">{{ (message.duration / 1000).toFixed(1) }}s</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="conversationHistory.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
              <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
            </svg>
          </div>
          <p>点击下方麦克风开始对话</p>
        </div>
      </div>
    </main>

    <!-- 底部控制区 -->
    <footer class="control-area">
      <div class="control-container">
        <!-- 清空按钮 -->
        <button
          v-if="conversationHistory.length > 0"
          class="clear-button"
          @click="clearHistory"
          title="清空对话"
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
        </button>

        <!-- 语音助手组件 -->
        <VoiceAssistant
          :auto-upload="true"
          @recording-start="onRecordingStart"
          @recording-stop="onRecordingStop"
          @recognition-success="onRecognitionSuccess"
          @recognition-error="onRecognitionError"
        />

        <!-- 设置按钮 -->
        <button class="settings-button" title="设置">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
          </svg>
        </button>
      </div>
    </footer>
  </div>
</template>

<style scoped>
.voice-assistant-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow: hidden;
}

/* 顶部状态栏 */
.app-header {
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.time {
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff6b6b;
  animation: pulse-dot 2s infinite;
}

.service-status.online .status-dot {
  background: #51cf66;
}

.greeting h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.greeting p {
  margin: 0;
  opacity: 0.8;
  font-size: 1rem;
}

/* 对话区域 */
.conversation-area {
  flex: 1;
  overflow: hidden;
  padding: 0 1rem;
}

.messages-container {
  height: 100%;
  overflow-y: auto;
  padding: 1rem 0;
  scroll-behavior: smooth;
}

.message {
  display: flex;
  margin-bottom: 1.5rem;
  animation: fadeInUp 0.3s ease-out;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0.75rem;
  flex-shrink: 0;
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.assistant-avatar {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.message-avatar svg {
  width: 20px;
  height: 20px;
  color: white;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.user-message .message-content {
  text-align: right;
}

.message-bubble {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 1.25rem;
  padding: 1rem 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.user-message .message-bubble {
  background: rgba(255, 255, 255, 0.25);
}

.message-bubble p {
  margin: 0 0 0.5rem 0;
  line-height: 1.5;
  word-wrap: break-word;
}

.message-meta {
  display: flex;
  gap: 0.75rem;
  font-size: 0.75rem;
  opacity: 0.7;
}

.user-message .message-meta {
  justify-content: flex-end;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  opacity: 0.6;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-icon svg {
  width: 100%;
  height: 100%;
}

/* 底部控制区 */
.control-area {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.control-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.clear-button,
.settings-button {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-button:hover,
.settings-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.clear-button svg,
.settings-button svg {
  width: 20px;
  height: 20px;
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }

  .greeting h1 {
    font-size: 1.5rem;
  }

  .conversation-area {
    padding: 0 0.75rem;
  }

  .message-content {
    max-width: 85%;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    margin: 0 0.5rem;
  }

  .message-avatar svg {
    width: 16px;
    height: 16px;
  }

  .message-bubble {
    padding: 0.75rem 1rem;
    border-radius: 1rem;
  }

  .control-area {
    padding: 1rem;
  }

  .clear-button,
  .settings-button {
    width: 40px;
    height: 40px;
  }

  .clear-button svg,
  .settings-button svg {
    width: 18px;
    height: 18px;
  }
}

/* 超小屏幕 */
@media (max-width: 480px) {
  .app-header {
    padding: 0.75rem;
  }

  .greeting h1 {
    font-size: 1.25rem;
  }

  .greeting p {
    font-size: 0.875rem;
  }

  .conversation-area {
    padding: 0 0.5rem;
  }

  .message {
    margin-bottom: 1rem;
  }

  .message-content {
    max-width: 90%;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 600px) {
  .app-header {
    padding: 0.75rem 1rem;
  }

  .greeting h1 {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
  }

  .greeting p {
    font-size: 0.875rem;
  }

  .control-area {
    padding: 1rem;
  }
}
</style>
