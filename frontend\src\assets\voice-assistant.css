/* 语音助手应用的基础样式 */
:root {
  /* 主色调 */
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --secondary-color: #764ba2;
  --accent-color: #4facfe;
  
  /* 状态颜色 */
  --success-color: #51cf66;
  --error-color: #ff6b6b;
  --warning-color: #feca57;
  --info-color: #74c0fc;
  
  /* 文本颜色 */
  --text-primary: rgba(255, 255, 255, 0.95);
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-muted: rgba(255, 255, 255, 0.5);
  
  /* 背景颜色 */
  --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-glass: rgba(255, 255, 255, 0.1);
  --bg-glass-strong: rgba(255, 255, 255, 0.15);
  --bg-overlay: rgba(0, 0, 0, 0.3);
  
  /* 边框和阴影 */
  --border-glass: rgba(255, 255, 255, 0.2);
  --border-strong: rgba(255, 255, 255, 0.3);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-primary: 0 8px 32px rgba(102, 126, 234, 0.4);
  
  /* 圆角 */
  --radius-xs: 0.25rem;
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-full: 50%;
  
  /* 间距 */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* 动画时长 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  
  /* 动画缓动 */
  --ease-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --text-primary: rgba(255, 255, 255, 0.95);
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-muted: rgba(255, 255, 255, 0.5);
  }
}

/* 浅色模式适配 */
@media (prefers-color-scheme: light) {
  :root {
    --text-primary: rgba(0, 0, 0, 0.87);
    --text-secondary: rgba(0, 0, 0, 0.6);
    --text-muted: rgba(0, 0, 0, 0.4);
    --bg-glass: rgba(255, 255, 255, 0.8);
    --bg-glass-strong: rgba(255, 255, 255, 0.9);
    --border-glass: rgba(255, 255, 255, 0.9);
    --border-strong: rgba(255, 255, 255, 1);
  }
}

/* 响应式断点变量 */
@media (max-width: 768px) {
  :root {
    --space-xs: 0.2rem;
    --space-sm: 0.4rem;
    --space-md: 0.8rem;
    --space-lg: 1.2rem;
    --space-xl: 1.6rem;
    --space-2xl: 2.4rem;
    
    --text-xs: 0.7rem;
    --text-sm: 0.8rem;
    --text-base: 0.9rem;
    --text-lg: 1rem;
    --text-xl: 1.1rem;
    --text-2xl: 1.3rem;
    --text-3xl: 1.6rem;
    --text-4xl: 2rem;
  }
}

@media (max-width: 480px) {
  :root {
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
  }
}

/* 工具类 */
.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass);
}

.glass-strong {
  background: var(--bg-glass-strong);
  backdrop-filter: blur(30px);
  border: 1px solid var(--border-strong);
}

.text-gradient {
  background: var(--bg-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 动画工具类 */
.fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.bounce-in {
  animation: bounceIn var(--duration-slow) var(--ease-bounce);
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式工具类 */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }
  
  .desktop-only {
    display: none;
  }
}

/* 无障碍支持 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 触摸优化 */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .glass {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.6);
  }
  
  .glass-strong {
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .fade-in,
  .slide-up,
  .bounce-in {
    animation: none;
  }
  
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(var(--space-md), env(safe-area-inset-top));
  }
  
  .safe-area-bottom {
    padding-bottom: max(var(--space-md), env(safe-area-inset-bottom));
  }
  
  .safe-area-left {
    padding-left: max(var(--space-md), env(safe-area-inset-left));
  }
  
  .safe-area-right {
    padding-right: max(var(--space-md), env(safe-area-inset-right));
  }
}
