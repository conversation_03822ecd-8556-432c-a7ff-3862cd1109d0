#!/usr/bin/env python3
"""
uv 启动脚本 - 语音识别后端服务

使用 uv 包管理器启动服务
使用方法: python start_server.py [dev|prod]
"""

import os
import sys
import platform
import subprocess
from pathlib import Path


def print_banner():
    """打印启动横幅"""
    print("=" * 50)
    print("语音识别系统后端服务 (uv)")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {platform.python_version()}")
    print("=" * 50)
    print()


def check_uv():
    """检查uv包管理器"""
    try:
        result = subprocess.run(['uv', '--version'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✅ uv包管理器: {result.stdout.strip()}")
            return True
        else:
            print("❌ uv包管理器不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ uv包管理器未安装")
        print("请运行: pip install uv")
        return False


def check_config_file():
    """检查配置文件"""
    config_file = Path("config.yaml")
    if not config_file.exists():
        print("❌ 配置文件 config.yaml 不存在")
        print("请确保配置文件存在并包含正确的阿里云API Key")
        return False

    print("✅ 配置文件检查通过")
    return True


def sync_dependencies():
    """同步依赖"""
    print("📦 同步项目依赖...")
    try:
        result = subprocess.run(['uv', 'sync'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 依赖同步完成")
            return True
        else:
            print(f"❌ 依赖同步失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 依赖同步异常: {e}")
        return False


def start_server(mode="dev"):
    """启动服务器"""
    if mode == "dev":
        print("\n🚀 启动开发服务器...")
        command = ['uv', 'run', 'dev']
    else:
        print("\n🚀 启动生产服务器...")
        command = ['uv', 'run', 'start']

    print("服务地址: http://localhost:8001")
    print("API文档: http://localhost:8001/docs")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)

    try:
        subprocess.run(command)
    except KeyboardInterrupt:
        print("\n\n🛑 服务已停止")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {e}")
        return False

    return True


def main():
    """主函数"""
    print_banner()

    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"工作目录: {script_dir.absolute()}")
    print()

    # 解析启动模式
    mode = "dev"
    if len(sys.argv) > 1:
        if sys.argv[1] in ["prod", "production"]:
            mode = "prod"
        elif sys.argv[1] in ["dev", "development"]:
            mode = "dev"
        else:
            print(f"未知模式: {sys.argv[1]}")
            print("支持的模式: dev, prod")
            return 1

    print(f"启动模式: {'开发' if mode == 'dev' else '生产'}")
    print()

    # 基础检查
    if not check_uv():
        return 1

    if not check_config_file():
        return 1

    # 同步依赖
    if not sync_dependencies():
        print("⚠️  依赖同步失败，尝试继续启动...")

    # 启动服务
    if not start_server(mode):
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
